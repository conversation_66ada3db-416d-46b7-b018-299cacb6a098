@tailwind base;
@tailwind components;
@tailwind utilities;

*,
*::before,
*::after {
  box-sizing: border-box;
}

html,
body,
#root,
.app {
  height: 100%;
  width: 100%;
  @apply text-sm;
  @apply bg-gray-500;
  @apply text-gray-900;
}

@media (min-width: 768px) {
  .custom-grid-rows {
    grid-template-rows: repeat(8, 20vh);
  }
}

@media (min-width: 1280px) {
  .custom-grid-rows {
    grid-template-rows: repeat(8, 7.5vh);
  }
}
