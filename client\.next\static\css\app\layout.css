/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[2].use[1]!./node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js??ruleSet[1].rules[14].oneOf[2].use[2]!./node_modules/next/font/google/target.css?{"path":"src\\app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"]}],"variableName":"inter"} ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/* cyrillic-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/55c55f0601d81cf3-s.woff2) format('woff2');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/26a46d62cd723877-s.woff2) format('woff2');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/97e0cb1ae144a2a9-s.woff2) format('woff2');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/581909926a08bbc8-s.woff2) format('woff2');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* vietnamese */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/df0a9ae256c0569c-s.woff2) format('woff2');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/8e9860b6e62d6359-s.woff2) format('woff2');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: '__Inter_e8ce0c';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url(/_next/static/media/e4af272ccee01ff0-s.p.woff2) format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}@font-face {font-family: '__Inter_Fallback_e8ce0c';src: local("Arial");ascent-override: 90.49%;descent-override: 22.56%;line-gap-override: 0.00%;size-adjust: 107.06%
}.__className_e8ce0c {font-family: '__Inter_e8ce0c', '__Inter_Fallback_e8ce0c';font-style: normal
}

/*!*****************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/app/globals.css ***!
  \*****************************************************************************************************************************************************************************************************************************************************************/
/*
! tailwindcss v3.4.4 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
  display: none;
}

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.inset-0 {
  inset: 0px;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-right-2 {
  right: -0.5rem;
}
.-top-2 {
  top: -0.5rem;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.top-1\/2 {
  top: 50%;
}
.top-20 {
  top: 5rem;
}
.z-20 {
  z-index: 20;
}
.z-40 {
  z-index: 40;
}
.col-span-1 {
  grid-column: span 1 / span 1;
}
.row-span-2 {
  grid-row: span 2 / span 2;
}
.row-span-3 {
  grid-row: span 3 / span 3;
}
.m-2 {
  margin: 0.5rem;
}
.m-5 {
  margin: 1.25rem;
}
.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-3 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.mb-10 {
  margin-bottom: 2.5rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-7 {
  margin-bottom: 1.75rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-7 {
  margin-top: 1.75rem;
}
.mt-8 {
  margin-top: 2rem;
}
.block {
  display: block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.hidden {
  display: none;
}
.h-3 {
  height: 0.75rem;
}
.h-4 {
  height: 1rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-7 {
  height: 1.75rem;
}
.h-9 {
  height: 2.25rem;
}
.h-full {
  height: 100%;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-11 {
  width: 2.75rem;
}
.w-3 {
  width: 0.75rem;
}
.w-4 {
  width: 1rem;
}
.w-5 {
  width: 1.25rem;
}
.w-6 {
  width: 1.5rem;
}
.w-72 {
  width: 18rem;
}
.w-9 {
  width: 2.25rem;
}
.w-96 {
  width: 24rem;
}
.w-full {
  width: 100%;
}
.min-w-full {
  min-width: 100%;
}
.max-w-full {
  max-width: 100%;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-grow {
  flex-grow: 1;
}
.basis-2\/5 {
  flex-basis: 40%;
}
.basis-3\/5 {
  flex-basis: 60%;
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.cursor-pointer {
  cursor: pointer;
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-start {
  justify-content: flex-start;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-10 {
  gap: 2.5rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-5 {
  gap: 1.25rem;
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.border {
  border-width: 1px;
}
.border-2 {
  border-width: 2px;
}
.border-\[1px\] {
  border-width: 1px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-l {
  border-left-width: 1px;
}
.border-solid {
  border-style: solid;
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: hsl(var(--twc-gray-200) / var(--twc-gray-200-opacity, var(--tw-border-opacity)));
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: hsl(var(--twc-gray-300) / var(--twc-gray-300-opacity, var(--tw-border-opacity)));
}
.border-gray-500 {
  --tw-border-opacity: 1;
  border-color: hsl(var(--twc-gray-500) / var(--twc-gray-500-opacity, var(--tw-border-opacity)));
}
.border-sky-300 {
  --tw-border-opacity: 1;
  border-color: rgb(125 211 252 / var(--tw-border-opacity));
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-blue-100) / var(--twc-blue-100-opacity, var(--tw-bg-opacity)));
}
.bg-blue-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-blue-200) / var(--twc-blue-200-opacity, var(--tw-bg-opacity)));
}
.bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-blue-50) / var(--twc-blue-50-opacity, var(--tw-bg-opacity)));
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-blue-500) / var(--twc-blue-500-opacity, var(--tw-bg-opacity)));
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-gray-100) / var(--twc-gray-100-opacity, var(--tw-bg-opacity)));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-gray-200) / var(--twc-gray-200-opacity, var(--tw-bg-opacity)));
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-gray-50) / var(--twc-gray-50-opacity, var(--tw-bg-opacity)));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-gray-500) / var(--twc-gray-500-opacity, var(--tw-bg-opacity)));
}
.bg-gray-600 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-gray-600) / var(--twc-gray-600-opacity, var(--tw-bg-opacity)));
}
.bg-gray-800 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-gray-800) / var(--twc-gray-800-opacity, var(--tw-bg-opacity)));
}
.bg-red-400 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-red-400) / var(--twc-red-400-opacity, var(--tw-bg-opacity)));
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-white) / var(--twc-white-opacity, var(--tw-bg-opacity)));
}
.bg-opacity-50 {
  --tw-bg-opacity: 0.5;
}
.p-2 {
  padding: 0.5rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-7 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.px-9 {
  padding-left: 2.25rem;
  padding-right: 2.25rem;
}
.px-\[0\.4rem\] {
  padding-left: 0.4rem;
  padding-right: 0.4rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-7 {
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}
.pb-16 {
  padding-bottom: 4rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-5 {
  padding-bottom: 1.25rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-3 {
  padding-left: 0.75rem;
}
.pr-10 {
  padding-right: 2.5rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-7 {
  padding-right: 1.75rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-5 {
  padding-top: 1.25rem;
}
.pt-8 {
  padding-top: 2rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-bold {
  font-weight: 700;
}
.font-extrabold {
  font-weight: 800;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.leading-none {
  line-height: 1;
}
.\!text-gray-200 {
  --tw-text-opacity: 1 !important;
  color: hsl(var(--twc-gray-200) / var(--twc-gray-200-opacity, var(--tw-text-opacity))) !important;
}
.\!text-gray-700 {
  --tw-text-opacity: 1 !important;
  color: hsl(var(--twc-gray-700) / var(--twc-gray-700-opacity, var(--tw-text-opacity))) !important;
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-blue-500) / var(--twc-blue-500-opacity, var(--tw-text-opacity)));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-blue-600) / var(--twc-blue-600-opacity, var(--tw-text-opacity)));
}
.text-gray-200 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-gray-200) / var(--twc-gray-200-opacity, var(--tw-text-opacity)));
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-gray-400) / var(--twc-gray-400-opacity, var(--tw-text-opacity)));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-gray-500) / var(--twc-gray-500-opacity, var(--tw-text-opacity)));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-gray-600) / var(--twc-gray-600-opacity, var(--tw-text-opacity)));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-gray-700) / var(--twc-gray-700-opacity, var(--tw-text-opacity)));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-gray-800) / var(--twc-gray-800-opacity, var(--tw-text-opacity)));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-gray-900) / var(--twc-gray-900-opacity, var(--tw-text-opacity)));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-green-500) / var(--twc-green-500-opacity, var(--tw-text-opacity)));
}
.text-red-100 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-red-100) / var(--twc-red-100-opacity, var(--tw-text-opacity)));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-red-500) / var(--twc-red-500-opacity, var(--tw-text-opacity)));
}
.text-white {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-white) / var(--twc-white-opacity, var(--tw-text-opacity)));
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.light,[data-theme="light"] {
  --twc-gray-50: 210 20% 98%;
  --twc-gray-100: 220 14.3% 95.9%;
  --twc-gray-200: 220 13% 91%;
  --twc-gray-300: 216 12.2% 83.9%;
  --twc-gray-400: 217.89999999999998 10.6% 64.9%;
  --twc-gray-500: 220 8.9% 46.1%;
  --twc-gray-600: 215 13.8% 34.1%;
  --twc-gray-700: 216.89999999999998 19.1% 26.7%;
  --twc-gray-800: 215 27.9% 16.9%;
  --twc-gray-900: 220.89999999999998 39.3% 11%;
  --twc-red-50: 0 85.7% 97.3%;
  --twc-red-100: 0 93.3% 94.1%;
  --twc-red-200: 0 96.3% 89.4%;
  --twc-red-300: 0 93.5% 81.8%;
  --twc-red-400: 0 90.6% 70.8%;
  --twc-red-500: 0 84.2% 60.2%;
  --twc-red-600: 0 72.2% 50.6%;
  --twc-red-700: 0 73.7% 41.8%;
  --twc-red-800: 0 70% 35.3%;
  --twc-red-900: 0 62.8% 30.6%;
  --twc-yellow-50: 54.5 91.7% 95.3%;
  --twc-yellow-100: 54.89999999999998 96.7% 88%;
  --twc-yellow-200: 52.80000000000001 98.3% 76.9%;
  --twc-yellow-300: 50.39999999999998 97.8% 63.5%;
  --twc-yellow-400: 47.89999999999998 95.8% 53.1%;
  --twc-yellow-500: 45.39999999999998 93.4% 47.5%;
  --twc-yellow-600: 40.60000000000002 96.1% 40.4%;
  --twc-yellow-700: 35.5 91.7% 32.9%;
  --twc-yellow-800: 31.80000000000001 81% 28.8%;
  --twc-yellow-900: 28.399999999999977 72.5% 25.7%;
  --twc-green-50: 138.5 76.5% 96.7%;
  --twc-green-100: 140.60000000000002 84.2% 92.5%;
  --twc-green-200: 141 78.9% 85.1%;
  --twc-green-300: 141.7 76.6% 73.1%;
  --twc-green-400: 141.89999999999998 69.2% 58%;
  --twc-green-500: 142.10000000000002 70.6% 45.3%;
  --twc-green-600: 142.10000000000002 76.2% 36.3%;
  --twc-green-700: 142.39999999999998 71.8% 29.2%;
  --twc-green-800: 142.8 64.2% 24.1%;
  --twc-green-900: 143.8 61.2% 20.2%;
  --twc-blue-50: 213.79999999999995 100% 96.9%;
  --twc-blue-100: 214.29999999999995 94.6% 92.7%;
  --twc-blue-200: 213.29999999999995 96.9% 87.3%;
  --twc-blue-300: 211.70000000000005 96.4% 78.4%;
  --twc-blue-400: 213.10000000000002 93.9% 67.8%;
  --twc-blue-500: 217.20000000000005 91.2% 59.8%;
  --twc-blue-600: 221.20000000000005 83.2% 53.3%;
  --twc-blue-700: 224.29999999999995 76.3% 48%;
  --twc-blue-800: 225.89999999999998 70.7% 40.2%;
  --twc-blue-900: 224.39999999999998 64.3% 32.9%;
  --twc-indigo-50: 225.89999999999998 100% 96.7%;
  --twc-indigo-100: 226.5 100% 93.9%;
  --twc-indigo-200: 228 96.5% 88.8%;
  --twc-indigo-300: 229.70000000000005 93.5% 81.8%;
  --twc-indigo-400: 234.5 89.5% 73.9%;
  --twc-indigo-500: 238.70000000000005 83.5% 66.7%;
  --twc-indigo-600: 243.39999999999998 75.4% 58.6%;
  --twc-indigo-700: 244.5 57.9% 50.6%;
  --twc-indigo-800: 243.70000000000005 54.5% 41.4%;
  --twc-indigo-900: 242.20000000000005 47.4% 34.3%;
  --twc-purple-50: 270 100% 98%;
  --twc-purple-100: 268.70000000000005 100% 95.5%;
  --twc-purple-200: 268.6 100% 91.8%;
  --twc-purple-300: 269.20000000000005 97.4% 85.1%;
  --twc-purple-400: 270 95.2% 75.3%;
  --twc-purple-500: 270.70000000000005 91% 65.1%;
  --twc-purple-600: 271.5 81.3% 55.9%;
  --twc-purple-700: 272.1 71.7% 47.1%;
  --twc-purple-800: 272.9 67.2% 39.4%;
  --twc-purple-900: 273.6 65.6% 32%;
  --twc-pink-50: 327.29999999999995 73.3% 97.1%;
  --twc-pink-100: 325.70000000000005 77.8% 94.7%;
  --twc-pink-200: 325.9 84.6% 89.8%;
  --twc-pink-300: 327.4 87.1% 81.8%;
  --twc-pink-400: 328.6 85.5% 70.2%;
  --twc-pink-500: 330.4 81.2% 60.4%;
  --twc-pink-600: 333.29999999999995 71.4% 50.6%;
  --twc-pink-700: 335.1 77.6% 42%;
  --twc-pink-800: 335.79999999999995 74.4% 35.3%;
  --twc-pink-900: 335.9 69% 30.4%;
  --twc-white: 0 0% 100%;
}
.dark,[data-theme="dark"] {
  --twc-gray-50: 220.89999999999998 39.3% 11%;
  --twc-gray-100: 215 27.9% 16.9%;
  --twc-gray-200: 216.89999999999998 19.1% 26.7%;
  --twc-gray-300: 215 13.8% 34.1%;
  --twc-gray-400: 220 8.9% 46.1%;
  --twc-gray-500: 217.89999999999998 10.6% 64.9%;
  --twc-gray-600: 216 12.2% 83.9%;
  --twc-gray-700: 220 13% 91%;
  --twc-gray-800: 220 14.3% 95.9%;
  --twc-gray-900: 210 20% 98%;
  --twc-red-50: 0 62.8% 30.6%;
  --twc-red-100: 0 70% 35.3%;
  --twc-red-200: 0 73.7% 41.8%;
  --twc-red-300: 0 72.2% 50.6%;
  --twc-red-400: 0 84.2% 60.2%;
  --twc-red-500: 0 90.6% 70.8%;
  --twc-red-600: 0 93.5% 81.8%;
  --twc-red-700: 0 96.3% 89.4%;
  --twc-red-800: 0 93.3% 94.1%;
  --twc-red-900: 0 85.7% 97.3%;
  --twc-yellow-50: 28.399999999999977 72.5% 25.7%;
  --twc-yellow-100: 31.80000000000001 81% 28.8%;
  --twc-yellow-200: 35.5 91.7% 32.9%;
  --twc-yellow-300: 40.60000000000002 96.1% 40.4%;
  --twc-yellow-400: 45.39999999999998 93.4% 47.5%;
  --twc-yellow-500: 47.89999999999998 95.8% 53.1%;
  --twc-yellow-600: 50.39999999999998 97.8% 63.5%;
  --twc-yellow-700: 52.80000000000001 98.3% 76.9%;
  --twc-yellow-800: 54.89999999999998 96.7% 88%;
  --twc-yellow-900: 54.5 91.7% 95.3%;
  --twc-green-50: 143.8 61.2% 20.2%;
  --twc-green-100: 142.8 64.2% 24.1%;
  --twc-green-200: 142.39999999999998 71.8% 29.2%;
  --twc-green-300: 142.10000000000002 76.2% 36.3%;
  --twc-green-400: 142.10000000000002 70.6% 45.3%;
  --twc-green-500: 141.89999999999998 69.2% 58%;
  --twc-green-600: 141.7 76.6% 73.1%;
  --twc-green-700: 141 78.9% 85.1%;
  --twc-green-800: 140.60000000000002 84.2% 92.5%;
  --twc-green-900: 138.5 76.5% 96.7%;
  --twc-blue-50: 224.39999999999998 64.3% 32.9%;
  --twc-blue-100: 225.89999999999998 70.7% 40.2%;
  --twc-blue-200: 224.29999999999995 76.3% 48%;
  --twc-blue-300: 221.20000000000005 83.2% 53.3%;
  --twc-blue-400: 217.20000000000005 91.2% 59.8%;
  --twc-blue-500: 213.10000000000002 93.9% 67.8%;
  --twc-blue-600: 211.70000000000005 96.4% 78.4%;
  --twc-blue-700: 213.29999999999995 96.9% 87.3%;
  --twc-blue-800: 214.29999999999995 94.6% 92.7%;
  --twc-blue-900: 213.79999999999995 100% 96.9%;
  --twc-indigo-50: 242.20000000000005 47.4% 34.3%;
  --twc-indigo-100: 243.70000000000005 54.5% 41.4%;
  --twc-indigo-200: 244.5 57.9% 50.6%;
  --twc-indigo-300: 243.39999999999998 75.4% 58.6%;
  --twc-indigo-400: 238.70000000000005 83.5% 66.7%;
  --twc-indigo-500: 234.5 89.5% 73.9%;
  --twc-indigo-600: 229.70000000000005 93.5% 81.8%;
  --twc-indigo-700: 228 96.5% 88.8%;
  --twc-indigo-800: 226.5 100% 93.9%;
  --twc-indigo-900: 225.89999999999998 100% 96.7%;
  --twc-purple-50: 273.6 65.6% 32%;
  --twc-purple-100: 272.9 67.2% 39.4%;
  --twc-purple-200: 272.1 71.7% 47.1%;
  --twc-purple-300: 271.5 81.3% 55.9%;
  --twc-purple-400: 270.70000000000005 91% 65.1%;
  --twc-purple-500: 270 95.2% 75.3%;
  --twc-purple-600: 269.20000000000005 97.4% 85.1%;
  --twc-purple-700: 268.6 100% 91.8%;
  --twc-purple-800: 268.70000000000005 100% 95.5%;
  --twc-purple-900: 270 100% 98%;
  --twc-pink-50: 335.9 69% 30.4%;
  --twc-pink-100: 335.79999999999995 74.4% 35.3%;
  --twc-pink-200: 335.1 77.6% 42%;
  --twc-pink-300: 333.29999999999995 71.4% 50.6%;
  --twc-pink-400: 330.4 81.2% 60.4%;
  --twc-pink-500: 328.6 85.5% 70.2%;
  --twc-pink-600: 327.4 87.1% 81.8%;
  --twc-pink-700: 325.9 84.6% 89.8%;
  --twc-pink-800: 325.70000000000005 77.8% 94.7%;
  --twc-pink-900: 327.29999999999995 73.3% 97.1%;
  --twc-white: 224 71.4% 4.1%;
  --twc-black: 210 20% 98%;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

html,
body,
#root,
.app {
  height: 100%;
  width: 100%;
  font-size: 0.875rem;
  line-height: 1.25rem;
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-gray-500) / var(--twc-gray-500-opacity, var(--tw-bg-opacity)));
  --tw-text-opacity: 1;
  color: hsl(var(--twc-gray-900) / var(--twc-gray-900-opacity, var(--tw-text-opacity)));
}

@media (min-width: 768px) {
  .custom-grid-rows {
    grid-template-rows: repeat(8, 20vh);
  }
}

@media (min-width: 1280px) {
  .custom-grid-rows {
    grid-template-rows: repeat(8, 7.5vh);
  }
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:left-\[2px\]::after {
  content: var(--tw-content);
  left: 2px;
}

.after\:top-\[2px\]::after {
  content: var(--tw-content);
  top: 2px;
}

.after\:h-5::after {
  content: var(--tw-content);
  height: 1.25rem;
}

.after\:w-5::after {
  content: var(--tw-content);
  width: 1.25rem;
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:border::after {
  content: var(--tw-content);
  border-width: 1px;
}

.after\:border-gray-300::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: hsl(var(--twc-gray-300) / var(--twc-gray-300-opacity, var(--tw-border-opacity)));
}

.after\:bg-white::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-white) / var(--twc-white-opacity, var(--tw-bg-opacity)));
}

.after\:transition-all::after {
  content: var(--tw-content);
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.after\:content-\[\'\'\]::after {
  --tw-content: '';
  content: var(--tw-content);
}

.hover\:bg-blue-100:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-blue-100) / var(--twc-blue-100-opacity, var(--tw-bg-opacity)));
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-blue-50) / var(--twc-blue-50-opacity, var(--tw-bg-opacity)));
}

.hover\:bg-blue-700:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-blue-700) / var(--twc-blue-700-opacity, var(--tw-bg-opacity)));
}

.hover\:bg-gray-700:hover {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-gray-700) / var(--twc-gray-700-opacity, var(--tw-bg-opacity)));
}

.hover\:text-blue-500:hover {
  --tw-text-opacity: 1;
  color: hsl(var(--twc-blue-500) / var(--twc-blue-500-opacity, var(--tw-text-opacity)));
}

.focus\:border-blue-500:focus {
  --tw-border-opacity: 1;
  border-color: hsl(var(--twc-blue-500) / var(--twc-blue-500-opacity, var(--tw-border-opacity)));
}

.focus\:border-indigo-500:focus {
  --tw-border-opacity: 1;
  border-color: hsl(var(--twc-indigo-500) / var(--twc-indigo-500-opacity, var(--tw-border-opacity)));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-indigo-500:focus {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--twc-indigo-500) / var(--twc-indigo-500-opacity, var(--tw-ring-opacity)));
}

.peer:checked ~ .peer-checked\:bg-blue-600 {
  --tw-bg-opacity: 1;
  background-color: hsl(var(--twc-blue-600) / var(--twc-blue-600-opacity, var(--tw-bg-opacity)));
}

.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
  content: var(--tw-content);
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.peer:checked ~ .peer-checked\:after\:border-white::after {
  content: var(--tw-content);
  --tw-border-opacity: 1;
  border-color: hsl(var(--twc-white) / var(--twc-white-opacity, var(--tw-border-opacity)));
}

.peer:focus ~ .peer-focus\:ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.peer:focus ~ .peer-focus\:ring-blue-400 {
  --tw-ring-opacity: 1;
  --tw-ring-color: hsl(var(--twc-blue-400) / var(--twc-blue-400-opacity, var(--tw-ring-opacity)));
}

@media (min-width: 640px) {

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

@media (min-width: 768px) {

  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:row-span-1 {
    grid-row: span 1 / span 1;
  }

  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:w-1\/3 {
    width: 33.333333%;
  }

  .md\:w-16 {
    width: 4rem;
  }

  .md\:w-60 {
    width: 15rem;
  }

  .md\:w-64 {
    width: 16rem;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:justify-normal {
    justify-content: normal;
  }

  .md\:p-6 {
    padding: 1.5rem;
  }

  .md\:pl-24 {
    padding-left: 6rem;
  }

  .md\:pl-72 {
    padding-left: 18rem;
  }
}

@media (min-width: 1280px) {

  .xl\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .xl\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .xl\:row-span-3 {
    grid-row: span 3 / span 3;
  }

  .xl\:row-span-6 {
    grid-row: span 6 / span 6;
  }

  .xl\:flex {
    display: flex;
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:items-start {
    align-items: flex-start;
  }

  .xl\:overflow-auto {
    overflow: auto;
  }
}

