"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-equals";
exports.ids = ["vendor-chunks/fast-equals"];
exports.modules = {

/***/ "(ssr)/./node_modules/fast-equals/dist/esm/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/fast-equals/dist/esm/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   circularDeepEqual: () => (/* binding */ circularDeepEqual),\n/* harmony export */   circularShallowEqual: () => (/* binding */ circularShallowEqual),\n/* harmony export */   createCustomEqual: () => (/* binding */ createCustomEqual),\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual),\n/* harmony export */   sameValueZeroEqual: () => (/* binding */ sameValueZeroEqual),\n/* harmony export */   shallowEqual: () => (/* binding */ shallowEqual),\n/* harmony export */   strictCircularDeepEqual: () => (/* binding */ strictCircularDeepEqual),\n/* harmony export */   strictCircularShallowEqual: () => (/* binding */ strictCircularShallowEqual),\n/* harmony export */   strictDeepEqual: () => (/* binding */ strictDeepEqual),\n/* harmony export */   strictShallowEqual: () => (/* binding */ strictShallowEqual)\n/* harmony export */ });\nvar getOwnPropertyNames = Object.getOwnPropertyNames, getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n/**\n * Combine two comparators into a single comparators.\n */\nfunction combineComparators(comparatorA, comparatorB) {\n    return function isEqual(a, b, state) {\n        return comparatorA(a, b, state) && comparatorB(a, b, state);\n    };\n}\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nfunction createIsCircular(areItemsEqual) {\n    return function isCircular(a, b, state) {\n        if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n            return areItemsEqual(a, b, state);\n        }\n        var cache = state.cache;\n        var cachedA = cache.get(a);\n        var cachedB = cache.get(b);\n        if (cachedA && cachedB) {\n            return cachedA === b && cachedB === a;\n        }\n        cache.set(a, b);\n        cache.set(b, a);\n        var result = areItemsEqual(a, b, state);\n        cache.delete(a);\n        cache.delete(b);\n        return result;\n    };\n}\n/**\n * Get the properties to strictly examine, which include both own properties that are\n * not enumerable and symbol properties.\n */\nfunction getStrictProperties(object) {\n    return getOwnPropertyNames(object).concat(getOwnPropertySymbols(object));\n}\n/**\n * Whether the object contains the property passed as an own property.\n */\nvar hasOwn = Object.hasOwn ||\n    (function (object, property) {\n        return hasOwnProperty.call(object, property);\n    });\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nfunction sameValueZeroEqual(a, b) {\n    return a || b ? a === b : a === b || (a !== a && b !== b);\n}\n\nvar OWNER = '_owner';\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor, keys = Object.keys;\n/**\n * Whether the arrays are equal in value.\n */\nfunction areArraysEqual(a, b, state) {\n    var index = a.length;\n    if (b.length !== index) {\n        return false;\n    }\n    while (index-- > 0) {\n        if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the dates passed are equal in value.\n */\nfunction areDatesEqual(a, b) {\n    return sameValueZeroEqual(a.getTime(), b.getTime());\n}\n/**\n * Whether the `Map`s are equal in value.\n */\nfunction areMapsEqual(a, b, state) {\n    if (a.size !== b.size) {\n        return false;\n    }\n    var matchedIndices = {};\n    var aIterable = a.entries();\n    var index = 0;\n    var aResult;\n    var bResult;\n    while ((aResult = aIterable.next())) {\n        if (aResult.done) {\n            break;\n        }\n        var bIterable = b.entries();\n        var hasMatch = false;\n        var matchIndex = 0;\n        while ((bResult = bIterable.next())) {\n            if (bResult.done) {\n                break;\n            }\n            var _a = aResult.value, aKey = _a[0], aValue = _a[1];\n            var _b = bResult.value, bKey = _b[0], bValue = _b[1];\n            if (!hasMatch &&\n                !matchedIndices[matchIndex] &&\n                (hasMatch =\n                    state.equals(aKey, bKey, index, matchIndex, a, b, state) &&\n                        state.equals(aValue, bValue, aKey, bKey, a, b, state))) {\n                matchedIndices[matchIndex] = true;\n            }\n            matchIndex++;\n        }\n        if (!hasMatch) {\n            return false;\n        }\n        index++;\n    }\n    return true;\n}\n/**\n * Whether the objects are equal in value.\n */\nfunction areObjectsEqual(a, b, state) {\n    var properties = keys(a);\n    var index = properties.length;\n    if (keys(b).length !== index) {\n        return false;\n    }\n    var property;\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while (index-- > 0) {\n        property = properties[index];\n        if (property === OWNER &&\n            (a.$$typeof || b.$$typeof) &&\n            a.$$typeof !== b.$$typeof) {\n            return false;\n        }\n        if (!hasOwn(b, property) ||\n            !state.equals(a[property], b[property], property, property, a, b, state)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the objects are equal in value with strict property checking.\n */\nfunction areObjectsEqualStrict(a, b, state) {\n    var properties = getStrictProperties(a);\n    var index = properties.length;\n    if (getStrictProperties(b).length !== index) {\n        return false;\n    }\n    var property;\n    var descriptorA;\n    var descriptorB;\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while (index-- > 0) {\n        property = properties[index];\n        if (property === OWNER &&\n            (a.$$typeof || b.$$typeof) &&\n            a.$$typeof !== b.$$typeof) {\n            return false;\n        }\n        if (!hasOwn(b, property)) {\n            return false;\n        }\n        if (!state.equals(a[property], b[property], property, property, a, b, state)) {\n            return false;\n        }\n        descriptorA = getOwnPropertyDescriptor(a, property);\n        descriptorB = getOwnPropertyDescriptor(b, property);\n        if ((descriptorA || descriptorB) &&\n            (!descriptorA ||\n                !descriptorB ||\n                descriptorA.configurable !== descriptorB.configurable ||\n                descriptorA.enumerable !== descriptorB.enumerable ||\n                descriptorA.writable !== descriptorB.writable)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the primitive wrappers passed are equal in value.\n */\nfunction arePrimitiveWrappersEqual(a, b) {\n    return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n/**\n * Whether the regexps passed are equal in value.\n */\nfunction areRegExpsEqual(a, b) {\n    return a.source === b.source && a.flags === b.flags;\n}\n/**\n * Whether the `Set`s are equal in value.\n */\nfunction areSetsEqual(a, b, state) {\n    if (a.size !== b.size) {\n        return false;\n    }\n    var matchedIndices = {};\n    var aIterable = a.values();\n    var aResult;\n    var bResult;\n    while ((aResult = aIterable.next())) {\n        if (aResult.done) {\n            break;\n        }\n        var bIterable = b.values();\n        var hasMatch = false;\n        var matchIndex = 0;\n        while ((bResult = bIterable.next())) {\n            if (bResult.done) {\n                break;\n            }\n            if (!hasMatch &&\n                !matchedIndices[matchIndex] &&\n                (hasMatch = state.equals(aResult.value, bResult.value, aResult.value, bResult.value, a, b, state))) {\n                matchedIndices[matchIndex] = true;\n            }\n            matchIndex++;\n        }\n        if (!hasMatch) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the TypedArray instances are equal in value.\n */\nfunction areTypedArraysEqual(a, b) {\n    var index = a.length;\n    if (b.length !== index) {\n        return false;\n    }\n    while (index-- > 0) {\n        if (a[index] !== b[index]) {\n            return false;\n        }\n    }\n    return true;\n}\n\nvar ARGUMENTS_TAG = '[object Arguments]';\nvar BOOLEAN_TAG = '[object Boolean]';\nvar DATE_TAG = '[object Date]';\nvar MAP_TAG = '[object Map]';\nvar NUMBER_TAG = '[object Number]';\nvar OBJECT_TAG = '[object Object]';\nvar REG_EXP_TAG = '[object RegExp]';\nvar SET_TAG = '[object Set]';\nvar STRING_TAG = '[object String]';\nvar isArray = Array.isArray;\nvar isTypedArray = typeof ArrayBuffer === 'function' && ArrayBuffer.isView\n    ? ArrayBuffer.isView\n    : null;\nvar assign = Object.assign;\nvar getTag = Object.prototype.toString.call.bind(Object.prototype.toString);\n/**\n * Create a comparator method based on the type-specific equality comparators passed.\n */\nfunction createEqualityComparator(_a) {\n    var areArraysEqual = _a.areArraysEqual, areDatesEqual = _a.areDatesEqual, areMapsEqual = _a.areMapsEqual, areObjectsEqual = _a.areObjectsEqual, arePrimitiveWrappersEqual = _a.arePrimitiveWrappersEqual, areRegExpsEqual = _a.areRegExpsEqual, areSetsEqual = _a.areSetsEqual, areTypedArraysEqual = _a.areTypedArraysEqual;\n    /**\n     * compare the value of the two objects and return true if they are equivalent in values\n     */\n    return function comparator(a, b, state) {\n        // If the items are strictly equal, no need to do a value comparison.\n        if (a === b) {\n            return true;\n        }\n        // If the items are not non-nullish objects, then the only possibility\n        // of them being equal but not strictly is if they are both `NaN`. Since\n        // `NaN` is uniquely not equal to itself, we can use self-comparison of\n        // both objects, which is faster than `isNaN()`.\n        if (a == null ||\n            b == null ||\n            typeof a !== 'object' ||\n            typeof b !== 'object') {\n            return a !== a && b !== b;\n        }\n        var constructor = a.constructor;\n        // Checks are listed in order of commonality of use-case:\n        //   1. Common complex object types (plain object, array)\n        //   2. Common data values (date, regexp)\n        //   3. Less-common complex object types (map, set)\n        //   4. Less-common data values (promise, primitive wrappers)\n        // Inherently this is both subjective and assumptive, however\n        // when reviewing comparable libraries in the wild this order\n        // appears to be generally consistent.\n        // Constructors should match, otherwise there is potential for false positives\n        // between class and subclass or custom object and POJO.\n        if (constructor !== b.constructor) {\n            return false;\n        }\n        // `isPlainObject` only checks against the object's own realm. Cross-realm\n        // comparisons are rare, and will be handled in the ultimate fallback, so\n        // we can avoid capturing the string tag.\n        if (constructor === Object) {\n            return areObjectsEqual(a, b, state);\n        }\n        // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n        // the string tag or doing an `instanceof` check.\n        if (isArray(a)) {\n            return areArraysEqual(a, b, state);\n        }\n        // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n        // capturing the string tag or comparing against all possible constructors.\n        if (isTypedArray != null && isTypedArray(a)) {\n            return areTypedArraysEqual(a, b, state);\n        }\n        // Try to fast-path equality checks for other complex object types in the\n        // same realm to avoid capturing the string tag. Strict equality is used\n        // instead of `instanceof` because it is more performant for the common\n        // use-case. If someone is subclassing a native class, it will be handled\n        // with the string tag comparison.\n        if (constructor === Date) {\n            return areDatesEqual(a, b, state);\n        }\n        if (constructor === RegExp) {\n            return areRegExpsEqual(a, b, state);\n        }\n        if (constructor === Map) {\n            return areMapsEqual(a, b, state);\n        }\n        if (constructor === Set) {\n            return areSetsEqual(a, b, state);\n        }\n        // Since this is a custom object, capture the string tag to determing its type.\n        // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n        var tag = getTag(a);\n        if (tag === DATE_TAG) {\n            return areDatesEqual(a, b, state);\n        }\n        if (tag === REG_EXP_TAG) {\n            return areRegExpsEqual(a, b, state);\n        }\n        if (tag === MAP_TAG) {\n            return areMapsEqual(a, b, state);\n        }\n        if (tag === SET_TAG) {\n            return areSetsEqual(a, b, state);\n        }\n        if (tag === OBJECT_TAG) {\n            // The exception for value comparison is custom `Promise`-like class instances. These should\n            // be treated the same as standard `Promise` objects, which means strict equality, and if\n            // it reaches this point then that strict equality comparison has already failed.\n            return (typeof a.then !== 'function' &&\n                typeof b.then !== 'function' &&\n                areObjectsEqual(a, b, state));\n        }\n        // If an arguments tag, it should be treated as a standard object.\n        if (tag === ARGUMENTS_TAG) {\n            return areObjectsEqual(a, b, state);\n        }\n        // As the penultimate fallback, check if the values passed are primitive wrappers. This\n        // is very rare in modern JS, which is why it is deprioritized compared to all other object\n        // types.\n        if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n            return arePrimitiveWrappersEqual(a, b, state);\n        }\n        // If not matching any tags that require a specific type of comparison, then we hard-code false because\n        // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n        //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n        //     comparison that can be made.\n        //   - For types that can be introspected, but rarely have requirements to be compared\n        //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n        //     use-cases (may be included in a future release, if requested enough).\n        //   - For types that can be introspected but do not have an objective definition of what\n        //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n        // In all cases, these decisions should be reevaluated based on changes to the language and\n        // common development practices.\n        return false;\n    };\n}\n/**\n * Create the configuration object used for building comparators.\n */\nfunction createEqualityComparatorConfig(_a) {\n    var circular = _a.circular, createCustomConfig = _a.createCustomConfig, strict = _a.strict;\n    var config = {\n        areArraysEqual: strict\n            ? areObjectsEqualStrict\n            : areArraysEqual,\n        areDatesEqual: areDatesEqual,\n        areMapsEqual: strict\n            ? combineComparators(areMapsEqual, areObjectsEqualStrict)\n            : areMapsEqual,\n        areObjectsEqual: strict\n            ? areObjectsEqualStrict\n            : areObjectsEqual,\n        arePrimitiveWrappersEqual: arePrimitiveWrappersEqual,\n        areRegExpsEqual: areRegExpsEqual,\n        areSetsEqual: strict\n            ? combineComparators(areSetsEqual, areObjectsEqualStrict)\n            : areSetsEqual,\n        areTypedArraysEqual: strict\n            ? areObjectsEqualStrict\n            : areTypedArraysEqual,\n    };\n    if (createCustomConfig) {\n        config = assign({}, config, createCustomConfig(config));\n    }\n    if (circular) {\n        var areArraysEqual$1 = createIsCircular(config.areArraysEqual);\n        var areMapsEqual$1 = createIsCircular(config.areMapsEqual);\n        var areObjectsEqual$1 = createIsCircular(config.areObjectsEqual);\n        var areSetsEqual$1 = createIsCircular(config.areSetsEqual);\n        config = assign({}, config, {\n            areArraysEqual: areArraysEqual$1,\n            areMapsEqual: areMapsEqual$1,\n            areObjectsEqual: areObjectsEqual$1,\n            areSetsEqual: areSetsEqual$1,\n        });\n    }\n    return config;\n}\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nfunction createInternalEqualityComparator(compare) {\n    return function (a, b, _indexOrKeyA, _indexOrKeyB, _parentA, _parentB, state) {\n        return compare(a, b, state);\n    };\n}\n/**\n * Create the `isEqual` function used by the consuming application.\n */\nfunction createIsEqual(_a) {\n    var circular = _a.circular, comparator = _a.comparator, createState = _a.createState, equals = _a.equals, strict = _a.strict;\n    if (createState) {\n        return function isEqual(a, b) {\n            var _a = createState(), _b = _a.cache, cache = _b === void 0 ? circular ? new WeakMap() : undefined : _b, meta = _a.meta;\n            return comparator(a, b, {\n                cache: cache,\n                equals: equals,\n                meta: meta,\n                strict: strict,\n            });\n        };\n    }\n    if (circular) {\n        return function isEqual(a, b) {\n            return comparator(a, b, {\n                cache: new WeakMap(),\n                equals: equals,\n                meta: undefined,\n                strict: strict,\n            });\n        };\n    }\n    var state = {\n        cache: undefined,\n        equals: equals,\n        meta: undefined,\n        strict: strict,\n    };\n    return function isEqual(a, b) {\n        return comparator(a, b, state);\n    };\n}\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nvar deepEqual = createCustomEqual();\n/**\n * Whether the items passed are deeply-equal in value based on strict comparison.\n */\nvar strictDeepEqual = createCustomEqual({ strict: true });\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nvar circularDeepEqual = createCustomEqual({ circular: true });\n/**\n * Whether the items passed are deeply-equal in value, including circular references,\n * based on strict comparison.\n */\nvar strictCircularDeepEqual = createCustomEqual({\n    circular: true,\n    strict: true,\n});\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nvar shallowEqual = createCustomEqual({\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value based on strict comparison\n */\nvar strictShallowEqual = createCustomEqual({\n    strict: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nvar circularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references,\n * based on strict comparison.\n */\nvar strictCircularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n    strict: true,\n});\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nfunction createCustomEqual(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.circular, circular = _a === void 0 ? false : _a, createCustomInternalComparator = options.createInternalComparator, createState = options.createState, _b = options.strict, strict = _b === void 0 ? false : _b;\n    var config = createEqualityComparatorConfig(options);\n    var comparator = createEqualityComparator(config);\n    var equals = createCustomInternalComparator\n        ? createCustomInternalComparator(comparator)\n        : createInternalEqualityComparator(comparator);\n    return createIsEqual({ circular: circular, comparator: comparator, createState: createState, equals: equals, strict: strict });\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-equals/dist/esm/index.mjs\n");

/***/ })

};
;