{"name": "inventory-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/material": "^5.16.0", "@mui/x-data-grid": "^7.9.0", "@reduxjs/toolkit": "^2.2.6", "axios": "^1.7.2", "dotenv": "^16.4.5", "lucide-react": "^0.407.0", "next": "^14.2.29", "numeral": "^2.0.6", "react": "^18", "react-dom": "^18", "react-redux": "^9.1.2", "recharts": "^2.12.7", "redux-persist": "^6.0.0", "uuid": "^10.0.0"}, "devDependencies": {"@types/node": "^20.14.10", "@types/numeral": "^2.0.5", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "14.2.4", "postcss": "^8", "tailwindcss": "^3.4.1", "tw-colors": "^3.3.1", "typescript": "^5"}}