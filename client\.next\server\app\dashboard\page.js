/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=F%3A%5CStockPilot%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CStockPilot%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=F%3A%5CStockPilot%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CStockPilot%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(rsc)/./src/app/dashboard/page.tsx\")), \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=F%3A%5CStockPilot%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CStockPilot%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5CdashboardWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5CdashboardWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboardWrapper.tsx */ \"(ssr)/./src/app/dashboardWrapper.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNTdG9ja1BpbG90JTVDJTVDY2xpZW50JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyU1QyU1QyU1QyU1Q2FwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNTdG9ja1BpbG90JTVDJTVDY2xpZW50JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkV3JhcHBlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNTdG9ja1BpbG90JTVDJTVDY2xpZW50JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdLQUF3SCIsInNvdXJjZXMiOlsid2VicGFjazovL2ludmVudG9yeS1tYW5hZ2VtZW50Lz8wNTdjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkY6XFxcXFN0b2NrUGlsb3RcXFxcY2xpZW50XFxcXHNyY1xcXFxhcHBcXFxcZGFzaGJvYXJkV3JhcHBlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5CdashboardWrapper.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/dashboard/page.tsx */ \"(ssr)/./src/app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkYlM0ElNUMlNUNTdG9ja1BpbG90JTVDJTVDY2xpZW50JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZGFzaGJvYXJkJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9LQUEwRiIsInNvdXJjZXMiOlsid2VicGFjazovL2ludmVudG9yeS1tYW5hZ2VtZW50Lz9hYzU0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRjpcXFxcU3RvY2tQaWxvdFxcXFxjbGllbnRcXFxcc3JjXFxcXGFwcFxcXFxkYXNoYm9hcmRcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22F%3A%5C%5CStockPilot%5C%5Cclient%5C%5Csrc%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(components)/Navbar/index.tsx":
/*!***********************************************!*\
  !*** ./src/app/(components)/Navbar/index.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/redux */ \"(ssr)/./src/app/redux.tsx\");\n/* harmony import */ var _state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/state */ \"(ssr)/./src/state/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Menu,Moon,Settings,Sun!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Navbar = ()=>{\n    const dispatch = (0,_app_redux__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n    const isSidebarCollapsed = (0,_app_redux__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)((state)=>state.global.isSidebarCollapsed);\n    const isDarkMode = (0,_app_redux__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)((state)=>state.global.isDarkMode);\n    const toggleSidebar = ()=>{\n        dispatch((0,_state__WEBPACK_IMPORTED_MODULE_2__.setIsSidebarCollapsed)(!isSidebarCollapsed));\n    };\n    const toggleDarkMode = ()=>{\n        dispatch((0,_state__WEBPACK_IMPORTED_MODULE_2__.setIsDarkMode)(!isDarkMode));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-between items-center w-full mb-7\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center gap-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-3 py-3 bg-gray-100 rounded-full hover:bg-blue-100\",\n                        onClick: toggleSidebar,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"search\",\n                                placeholder: \"Start type to search groups & products\",\n                                className: \"pl-10 pr-4 py-2 w-50 md:w-60 border-2 border-gray-300 bg-white rounded-lg focus:outline-none focus:border-blue-500\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-non\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"text-gray-500\",\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                lineNumber: 43,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center gap-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex justify-between items-center gap-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleDarkMode,\n                                    children: isDarkMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"cursor-pointer text-gray-500\",\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"cursor-pointer text-gray-500\",\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"cursor-pointer text-gray-500\",\n                                        size: 24\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-2 -right-2 inline-flex items-center justify-center px-[0.4rem] py-1 text-xs font-semibold leading-none text-red-100 bg-red-400 rounded-full\",\n                                        children: \"3\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"w-0 h-7 border border-solid border-l border-gray-300 mx-3\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3 cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-9 h-9\",\n                                        children: \"image\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: \"Ed Roh\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        href: \"/settings\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Menu_Moon_Settings_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"cursor-pointer text-gray-500\",\n                            size: 24\n                        }, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Navbar\\\\index.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(components)/Navbar/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(components)/Rating/index.tsx":
/*!***********************************************!*\
  !*** ./src/app/(components)/Rating/index.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Star!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nconst Rating = ({ rating })=>{\n    return [\n        1,\n        2,\n        3,\n        4,\n        5\n    ].map((index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Star_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            color: index <= rating ? \"#FFC107\" : \"#E4E5E9\",\n            className: \"w-4 h-4\"\n        }, index, false, {\n            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Rating\\\\index.tsx\",\n            lineNumber: 10,\n            columnNumber: 5\n        }, undefined));\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Rating);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwLyhjb21wb25lbnRzKS9SYXRpbmcvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBb0M7QUFDVjtBQU0xQixNQUFNRSxTQUFTLENBQUMsRUFBRUMsTUFBTSxFQUFlO0lBQ3JDLE9BQU87UUFBQztRQUFHO1FBQUc7UUFBRztRQUFHO0tBQUUsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLHNCQUMxQiw4REFBQ0wsZ0ZBQUlBO1lBRUhNLE9BQU9ELFNBQVNGLFNBQVMsWUFBWTtZQUNyQ0ksV0FBVTtXQUZMRjs7Ozs7QUFLWDtBQUVBLGlFQUFlSCxNQUFNQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW52ZW50b3J5LW1hbmFnZW1lbnQvLi9zcmMvYXBwLyhjb21wb25lbnRzKS9SYXRpbmcvaW5kZXgudHN4P2Y2ZWIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU3RhciB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcblxudHlwZSBSYXRpbmdQcm9wcyA9IHtcbiAgcmF0aW5nOiBudW1iZXI7XG59O1xuXG5jb25zdCBSYXRpbmcgPSAoeyByYXRpbmcgfTogUmF0aW5nUHJvcHMpID0+IHtcbiAgcmV0dXJuIFsxLCAyLCAzLCA0LCA1XS5tYXAoKGluZGV4KSA9PiAoXG4gICAgPFN0YXJcbiAgICAgIGtleT17aW5kZXh9XG4gICAgICBjb2xvcj17aW5kZXggPD0gcmF0aW5nID8gXCIjRkZDMTA3XCIgOiBcIiNFNEU1RTlcIn1cbiAgICAgIGNsYXNzTmFtZT1cInctNCBoLTRcIlxuICAgIC8+XG4gICkpO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgUmF0aW5nO1xuIl0sIm5hbWVzIjpbIlN0YXIiLCJSZWFjdCIsIlJhdGluZyIsInJhdGluZyIsIm1hcCIsImluZGV4IiwiY29sb3IiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(components)/Rating/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/(components)/Sidebar/index.tsx":
/*!************************************************!*\
  !*** ./src/app/(components)/Sidebar/index.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/redux */ \"(ssr)/./src/app/redux.tsx\");\n/* harmony import */ var _state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/state */ \"(ssr)/./src/state/index.ts\");\n/* harmony import */ var _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,CircleDollarSign,Clipboard,Layout,Menu,SlidersHorizontal,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,CircleDollarSign,Clipboard,Layout,Menu,SlidersHorizontal,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/panels-top-left.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,CircleDollarSign,Clipboard,Layout,Menu,SlidersHorizontal,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/archive.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,CircleDollarSign,Clipboard,Layout,Menu,SlidersHorizontal,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clipboard.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,CircleDollarSign,Clipboard,Layout,Menu,SlidersHorizontal,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,CircleDollarSign,Clipboard,Layout,Menu,SlidersHorizontal,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sliders-horizontal.js\");\n/* harmony import */ var _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Archive,CircleDollarSign,Clipboard,Layout,Menu,SlidersHorizontal,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst SidebarLink = ({ href, icon: Icon, label, isCollapsed })=>{\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.usePathname)();\n    const isActive = pathname === href || pathname === \"/\" && href === \"/dashboard\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        href: href,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `cursor-pointer flex items-center ${isCollapsed ? \"justify-center py-4\" : \"justify-start px-8 py-4\"}\n        hover:text-blue-500 hover:bg-blue-100 gap-3 transition-colors ${isActive ? \"bg-blue-200 text-white\" : \"\"}\n      }`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"w-6 h-6 !text-gray-700\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: `${isCollapsed ? \"hidden\" : \"block\"} font-medium text-gray-700`,\n                    children: label\n                }, void 0, false, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, undefined);\n};\nconst Sidebar = ()=>{\n    const dispatch = (0,_app_redux__WEBPACK_IMPORTED_MODULE_1__.useAppDispatch)();\n    const isSidebarCollapsed = (0,_app_redux__WEBPACK_IMPORTED_MODULE_1__.useAppSelector)((state)=>state.global.isSidebarCollapsed);\n    const toggleSidebar = ()=>{\n        dispatch((0,_state__WEBPACK_IMPORTED_MODULE_2__.setIsSidebarCollapsed)(!isSidebarCollapsed));\n    };\n    const sidebarClassNames = `fixed flex flex-col ${isSidebarCollapsed ? \"w-0 md:w-16\" : \"w-72 md:w-64\"} bg-white transition-all duration-300 overflow-hidden h-full shadow-md z-40`;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: sidebarClassNames,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex gap-3 justify-between md:justify-normal items-center pt-8 ${isSidebarCollapsed ? \"px-5\" : \"px-8\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"logo\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: `${isSidebarCollapsed ? \"hidden\" : \"block\"} font-extrabold text-2xl`,\n                        children: \"EDSTOCK\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"md:hidden px-3 py-3 bg-gray-100 rounded-full hover:bg-blue-100\",\n                        onClick: toggleSidebar,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-grow mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarLink, {\n                        href: \"/dashboard\",\n                        icon: _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                        label: \"Dashboard\",\n                        isCollapsed: isSidebarCollapsed\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarLink, {\n                        href: \"/inventory\",\n                        icon: _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                        label: \"Inventory\",\n                        isCollapsed: isSidebarCollapsed\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarLink, {\n                        href: \"/products\",\n                        icon: _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                        label: \"Products\",\n                        isCollapsed: isSidebarCollapsed\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarLink, {\n                        href: \"/users\",\n                        icon: _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                        label: \"Users\",\n                        isCollapsed: isSidebarCollapsed\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarLink, {\n                        href: \"/settings\",\n                        icon: _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                        label: \"Settings\",\n                        isCollapsed: isSidebarCollapsed\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarLink, {\n                        href: \"/expenses\",\n                        icon: _barrel_optimize_names_Archive_CircleDollarSign_Clipboard_Layout_Menu_SlidersHorizontal_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                        label: \"Expenses\",\n                        isCollapsed: isSidebarCollapsed\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${isSidebarCollapsed ? \"hidden\" : \"block\"} mb-10`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-xs text-gray-500\",\n                    children: \"\\xa9 2024 Edstock\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n                lineNumber: 142,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\(components)\\\\Sidebar\\\\index.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Sidebar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwLyhjb21wb25lbnRzKS9TaWRlYmFyL2luZGV4LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUU2RDtBQUNiO0FBVTFCO0FBRU87QUFDaUI7QUFDcEI7QUFTMUIsTUFBTWEsY0FBYyxDQUFDLEVBQ25CQyxJQUFJLEVBQ0pDLE1BQU1DLElBQUksRUFDVkMsS0FBSyxFQUNMQyxXQUFXLEVBQ007SUFDakIsTUFBTUMsV0FBV1IsNERBQVdBO0lBQzVCLE1BQU1TLFdBQ0pELGFBQWFMLFFBQVNLLGFBQWEsT0FBT0wsU0FBUztJQUVyRCxxQkFDRSw4REFBQ0osaURBQUlBO1FBQUNJLE1BQU1BO2tCQUNWLDRFQUFDTztZQUNDQyxXQUFXLENBQUMsaUNBQWlDLEVBQzNDSixjQUFjLHdCQUF3QiwwQkFDdkM7c0VBQzZELEVBQzVERSxXQUFXLDJCQUEyQixHQUN2QztPQUNGLENBQUM7OzhCQUVBLDhEQUFDSjtvQkFBS00sV0FBVTs7Ozs7OzhCQUVoQiw4REFBQ0M7b0JBQ0NELFdBQVcsQ0FBQyxFQUNWSixjQUFjLFdBQVcsUUFDMUIsMEJBQTBCLENBQUM7OEJBRTNCRDs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWDtBQUVBLE1BQU1PLFVBQVU7SUFDZCxNQUFNQyxXQUFXekIsMERBQWNBO0lBQy9CLE1BQU0wQixxQkFBcUJ6QiwwREFBY0EsQ0FDdkMsQ0FBQzBCLFFBQVVBLE1BQU1DLE1BQU0sQ0FBQ0Ysa0JBQWtCO0lBRzVDLE1BQU1HLGdCQUFnQjtRQUNwQkosU0FBU3ZCLDZEQUFxQkEsQ0FBQyxDQUFDd0I7SUFDbEM7SUFFQSxNQUFNSSxvQkFBb0IsQ0FBQyxvQkFBb0IsRUFDN0NKLHFCQUFxQixnQkFBZ0IsZUFDdEMsMkVBQTJFLENBQUM7SUFFN0UscUJBQ0UsOERBQUNMO1FBQUlDLFdBQVdROzswQkFFZCw4REFBQ1Q7Z0JBQ0NDLFdBQVcsQ0FBQywrREFBK0QsRUFDekVJLHFCQUFxQixTQUFTLE9BQy9CLENBQUM7O2tDQUVGLDhEQUFDTDtrQ0FBSTs7Ozs7O2tDQUNMLDhEQUFDVTt3QkFDQ1QsV0FBVyxDQUFDLEVBQ1ZJLHFCQUFxQixXQUFXLFFBQ2pDLHdCQUF3QixDQUFDO2tDQUMzQjs7Ozs7O2tDQUlELDhEQUFDTTt3QkFDQ1YsV0FBVTt3QkFDVlcsU0FBU0o7a0NBRVQsNEVBQUN0QixpSkFBSUE7NEJBQUNlLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUtwQiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDVDt3QkFDQ0MsTUFBSzt3QkFDTEMsTUFBTVQsaUpBQU1BO3dCQUNaVyxPQUFNO3dCQUNOQyxhQUFhUTs7Ozs7O2tDQUVmLDhEQUFDYjt3QkFDQ0MsTUFBSzt3QkFDTEMsTUFBTVosaUpBQU9BO3dCQUNiYyxPQUFNO3dCQUNOQyxhQUFhUTs7Ozs7O2tDQUVmLDhEQUFDYjt3QkFDQ0MsTUFBSzt3QkFDTEMsTUFBTVYsaUpBQVNBO3dCQUNmWSxPQUFNO3dCQUNOQyxhQUFhUTs7Ozs7O2tDQUVmLDhEQUFDYjt3QkFDQ0MsTUFBSzt3QkFDTEMsTUFBTU4sa0pBQUlBO3dCQUNWUSxPQUFNO3dCQUNOQyxhQUFhUTs7Ozs7O2tDQUVmLDhEQUFDYjt3QkFDQ0MsTUFBSzt3QkFDTEMsTUFBTVAsa0pBQWlCQTt3QkFDdkJTLE9BQU07d0JBQ05DLGFBQWFROzs7Ozs7a0NBRWYsOERBQUNiO3dCQUNDQyxNQUFLO3dCQUNMQyxNQUFNWCxrSkFBZ0JBO3dCQUN0QmEsT0FBTTt3QkFDTkMsYUFBYVE7Ozs7Ozs7Ozs7OzswQkFLakIsOERBQUNMO2dCQUFJQyxXQUFXLENBQUMsRUFBRUkscUJBQXFCLFdBQVcsUUFBUSxNQUFNLENBQUM7MEJBQ2hFLDRFQUFDUTtvQkFBRVosV0FBVTs4QkFBb0M7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSXpEO0FBRUEsaUVBQWVFLE9BQU9BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnZlbnRvcnktbWFuYWdlbWVudC8uL3NyYy9hcHAvKGNvbXBvbmVudHMpL1NpZGViYXIvaW5kZXgudHN4PzJkYzAiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IHVzZUFwcERpc3BhdGNoLCB1c2VBcHBTZWxlY3RvciB9IGZyb20gXCJAL2FwcC9yZWR1eFwiO1xuaW1wb3J0IHsgc2V0SXNTaWRlYmFyQ29sbGFwc2VkIH0gZnJvbSBcIkAvc3RhdGVcIjtcbmltcG9ydCB7XG4gIEFyY2hpdmUsXG4gIENpcmNsZURvbGxhclNpZ24sXG4gIENsaXBib2FyZCxcbiAgTGF5b3V0LFxuICBMdWNpZGVJY29uLFxuICBNZW51LFxuICBTbGlkZXJzSG9yaXpvbnRhbCxcbiAgVXNlcixcbn0gZnJvbSBcImx1Y2lkZS1yZWFjdFwiO1xuaW1wb3J0IEltYWdlIGZyb20gXCJuZXh0L2ltYWdlXCI7XG5pbXBvcnQgTGluayBmcm9tIFwibmV4dC9saW5rXCI7XG5pbXBvcnQgeyB1c2VQYXRobmFtZSB9IGZyb20gXCJuZXh0L25hdmlnYXRpb25cIjtcbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcblxuaW50ZXJmYWNlIFNpZGViYXJMaW5rUHJvcHMge1xuICBocmVmOiBzdHJpbmc7XG4gIGljb246IEx1Y2lkZUljb247XG4gIGxhYmVsOiBzdHJpbmc7XG4gIGlzQ29sbGFwc2VkOiBib29sZWFuO1xufVxuXG5jb25zdCBTaWRlYmFyTGluayA9ICh7XG4gIGhyZWYsXG4gIGljb246IEljb24sXG4gIGxhYmVsLFxuICBpc0NvbGxhcHNlZCxcbn06IFNpZGViYXJMaW5rUHJvcHMpID0+IHtcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xuICBjb25zdCBpc0FjdGl2ZSA9XG4gICAgcGF0aG5hbWUgPT09IGhyZWYgfHwgKHBhdGhuYW1lID09PSBcIi9cIiAmJiBocmVmID09PSBcIi9kYXNoYm9hcmRcIik7XG5cbiAgcmV0dXJuIChcbiAgICA8TGluayBocmVmPXtocmVmfT5cbiAgICAgIDxkaXZcbiAgICAgICAgY2xhc3NOYW1lPXtgY3Vyc29yLXBvaW50ZXIgZmxleCBpdGVtcy1jZW50ZXIgJHtcbiAgICAgICAgICBpc0NvbGxhcHNlZCA/IFwianVzdGlmeS1jZW50ZXIgcHktNFwiIDogXCJqdXN0aWZ5LXN0YXJ0IHB4LTggcHktNFwiXG4gICAgICAgIH1cbiAgICAgICAgaG92ZXI6dGV4dC1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTEwMCBnYXAtMyB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgIGlzQWN0aXZlID8gXCJiZy1ibHVlLTIwMCB0ZXh0LXdoaXRlXCIgOiBcIlwiXG4gICAgICAgIH1cbiAgICAgIH1gfVxuICAgICAgPlxuICAgICAgICA8SWNvbiBjbGFzc05hbWU9XCJ3LTYgaC02ICF0ZXh0LWdyYXktNzAwXCIgLz5cblxuICAgICAgICA8c3BhblxuICAgICAgICAgIGNsYXNzTmFtZT17YCR7XG4gICAgICAgICAgICBpc0NvbGxhcHNlZCA/IFwiaGlkZGVuXCIgOiBcImJsb2NrXCJcbiAgICAgICAgICB9IGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDBgfVxuICAgICAgICA+XG4gICAgICAgICAge2xhYmVsfVxuICAgICAgICA8L3NwYW4+XG4gICAgICA8L2Rpdj5cbiAgICA8L0xpbms+XG4gICk7XG59O1xuXG5jb25zdCBTaWRlYmFyID0gKCkgPT4ge1xuICBjb25zdCBkaXNwYXRjaCA9IHVzZUFwcERpc3BhdGNoKCk7XG4gIGNvbnN0IGlzU2lkZWJhckNvbGxhcHNlZCA9IHVzZUFwcFNlbGVjdG9yKFxuICAgIChzdGF0ZSkgPT4gc3RhdGUuZ2xvYmFsLmlzU2lkZWJhckNvbGxhcHNlZFxuICApO1xuXG4gIGNvbnN0IHRvZ2dsZVNpZGViYXIgPSAoKSA9PiB7XG4gICAgZGlzcGF0Y2goc2V0SXNTaWRlYmFyQ29sbGFwc2VkKCFpc1NpZGViYXJDb2xsYXBzZWQpKTtcbiAgfTtcblxuICBjb25zdCBzaWRlYmFyQ2xhc3NOYW1lcyA9IGBmaXhlZCBmbGV4IGZsZXgtY29sICR7XG4gICAgaXNTaWRlYmFyQ29sbGFwc2VkID8gXCJ3LTAgbWQ6dy0xNlwiIDogXCJ3LTcyIG1kOnctNjRcIlxuICB9IGJnLXdoaXRlIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBvdmVyZmxvdy1oaWRkZW4gaC1mdWxsIHNoYWRvdy1tZCB6LTQwYDtcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtzaWRlYmFyQ2xhc3NOYW1lc30+XG4gICAgICB7LyogVE9QIExPR08gKi99XG4gICAgICA8ZGl2XG4gICAgICAgIGNsYXNzTmFtZT17YGZsZXggZ2FwLTMganVzdGlmeS1iZXR3ZWVuIG1kOmp1c3RpZnktbm9ybWFsIGl0ZW1zLWNlbnRlciBwdC04ICR7XG4gICAgICAgICAgaXNTaWRlYmFyQ29sbGFwc2VkID8gXCJweC01XCIgOiBcInB4LThcIlxuICAgICAgICB9YH1cbiAgICAgID5cbiAgICAgICAgPGRpdj5sb2dvPC9kaXY+XG4gICAgICAgIDxoMVxuICAgICAgICAgIGNsYXNzTmFtZT17YCR7XG4gICAgICAgICAgICBpc1NpZGViYXJDb2xsYXBzZWQgPyBcImhpZGRlblwiIDogXCJibG9ja1wiXG4gICAgICAgICAgfSBmb250LWV4dHJhYm9sZCB0ZXh0LTJ4bGB9XG4gICAgICAgID5cbiAgICAgICAgICBFRFNUT0NLXG4gICAgICAgIDwvaDE+XG5cbiAgICAgICAgPGJ1dHRvblxuICAgICAgICAgIGNsYXNzTmFtZT1cIm1kOmhpZGRlbiBweC0zIHB5LTMgYmctZ3JheS0xMDAgcm91bmRlZC1mdWxsIGhvdmVyOmJnLWJsdWUtMTAwXCJcbiAgICAgICAgICBvbkNsaWNrPXt0b2dnbGVTaWRlYmFyfVxuICAgICAgICA+XG4gICAgICAgICAgPE1lbnUgY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBMSU5LUyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1ncm93IG10LThcIj5cbiAgICAgICAgPFNpZGViYXJMaW5rXG4gICAgICAgICAgaHJlZj1cIi9kYXNoYm9hcmRcIlxuICAgICAgICAgIGljb249e0xheW91dH1cbiAgICAgICAgICBsYWJlbD1cIkRhc2hib2FyZFwiXG4gICAgICAgICAgaXNDb2xsYXBzZWQ9e2lzU2lkZWJhckNvbGxhcHNlZH1cbiAgICAgICAgLz5cbiAgICAgICAgPFNpZGViYXJMaW5rXG4gICAgICAgICAgaHJlZj1cIi9pbnZlbnRvcnlcIlxuICAgICAgICAgIGljb249e0FyY2hpdmV9XG4gICAgICAgICAgbGFiZWw9XCJJbnZlbnRvcnlcIlxuICAgICAgICAgIGlzQ29sbGFwc2VkPXtpc1NpZGViYXJDb2xsYXBzZWR9XG4gICAgICAgIC8+XG4gICAgICAgIDxTaWRlYmFyTGlua1xuICAgICAgICAgIGhyZWY9XCIvcHJvZHVjdHNcIlxuICAgICAgICAgIGljb249e0NsaXBib2FyZH1cbiAgICAgICAgICBsYWJlbD1cIlByb2R1Y3RzXCJcbiAgICAgICAgICBpc0NvbGxhcHNlZD17aXNTaWRlYmFyQ29sbGFwc2VkfVxuICAgICAgICAvPlxuICAgICAgICA8U2lkZWJhckxpbmtcbiAgICAgICAgICBocmVmPVwiL3VzZXJzXCJcbiAgICAgICAgICBpY29uPXtVc2VyfVxuICAgICAgICAgIGxhYmVsPVwiVXNlcnNcIlxuICAgICAgICAgIGlzQ29sbGFwc2VkPXtpc1NpZGViYXJDb2xsYXBzZWR9XG4gICAgICAgIC8+XG4gICAgICAgIDxTaWRlYmFyTGlua1xuICAgICAgICAgIGhyZWY9XCIvc2V0dGluZ3NcIlxuICAgICAgICAgIGljb249e1NsaWRlcnNIb3Jpem9udGFsfVxuICAgICAgICAgIGxhYmVsPVwiU2V0dGluZ3NcIlxuICAgICAgICAgIGlzQ29sbGFwc2VkPXtpc1NpZGViYXJDb2xsYXBzZWR9XG4gICAgICAgIC8+XG4gICAgICAgIDxTaWRlYmFyTGlua1xuICAgICAgICAgIGhyZWY9XCIvZXhwZW5zZXNcIlxuICAgICAgICAgIGljb249e0NpcmNsZURvbGxhclNpZ259XG4gICAgICAgICAgbGFiZWw9XCJFeHBlbnNlc1wiXG4gICAgICAgICAgaXNDb2xsYXBzZWQ9e2lzU2lkZWJhckNvbGxhcHNlZH1cbiAgICAgICAgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogRk9PVEVSICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9e2Ake2lzU2lkZWJhckNvbGxhcHNlZCA/IFwiaGlkZGVuXCIgOiBcImJsb2NrXCJ9IG1iLTEwYH0+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQteHMgdGV4dC1ncmF5LTUwMFwiPiZjb3B5OyAyMDI0IEVkc3RvY2s8L3A+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFNpZGViYXI7XG4iXSwibmFtZXMiOlsidXNlQXBwRGlzcGF0Y2giLCJ1c2VBcHBTZWxlY3RvciIsInNldElzU2lkZWJhckNvbGxhcHNlZCIsIkFyY2hpdmUiLCJDaXJjbGVEb2xsYXJTaWduIiwiQ2xpcGJvYXJkIiwiTGF5b3V0IiwiTWVudSIsIlNsaWRlcnNIb3Jpem9udGFsIiwiVXNlciIsIkxpbmsiLCJ1c2VQYXRobmFtZSIsIlJlYWN0IiwiU2lkZWJhckxpbmsiLCJocmVmIiwiaWNvbiIsIkljb24iLCJsYWJlbCIsImlzQ29sbGFwc2VkIiwicGF0aG5hbWUiLCJpc0FjdGl2ZSIsImRpdiIsImNsYXNzTmFtZSIsInNwYW4iLCJTaWRlYmFyIiwiZGlzcGF0Y2giLCJpc1NpZGViYXJDb2xsYXBzZWQiLCJzdGF0ZSIsImdsb2JhbCIsInRvZ2dsZVNpZGViYXIiLCJzaWRlYmFyQ2xhc3NOYW1lcyIsImgxIiwiYnV0dG9uIiwib25DbGljayIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(components)/Sidebar/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboardWrapper.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboardWrapper.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_components_Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/(components)/Navbar */ \"(ssr)/./src/app/(components)/Navbar/index.tsx\");\n/* harmony import */ var _app_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/(components)/Sidebar */ \"(ssr)/./src/app/(components)/Sidebar/index.tsx\");\n/* harmony import */ var _redux__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./redux */ \"(ssr)/./src/app/redux.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst DashboardLayout = ({ children })=>{\n    const isSidebarCollapsed = (0,_redux__WEBPACK_IMPORTED_MODULE_4__.useAppSelector)((state)=>state.global.isSidebarCollapsed);\n    const isDarkMode = (0,_redux__WEBPACK_IMPORTED_MODULE_4__.useAppSelector)((state)=>state.global.isDarkMode);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isDarkMode) {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.add(\"light\");\n        }\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${isDarkMode ? \"dark\" : \"light\"} flex bg-gray-50 text-gray-900 w-full min-h-screen`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboardWrapper.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: `flex flex-col w-full h-full py-7 px-9 bg-gray-50 ${isSidebarCollapsed ? \"md:pl-24\" : \"md:pl-72\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_components_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboardWrapper.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, undefined),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboardWrapper.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboardWrapper.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\nconst DashboardWrapper = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_redux__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DashboardLayout, {\n            children: children\n        }, void 0, false, {\n            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboardWrapper.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboardWrapper.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DashboardWrapper);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboardWrapper.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/CardExpenseSummary.tsx":
/*!**************************************************!*\
  !*** ./src/app/dashboard/CardExpenseSummary.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _state_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/state/api */ \"(ssr)/./src/state/api.ts\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Cell_Pie_PieChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Cell,Pie,PieChart,ResponsiveContainer!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Cell_Pie_PieChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Cell,Pie,PieChart,ResponsiveContainer!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Cell_Pie_PieChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Cell,Pie,PieChart,ResponsiveContainer!=!recharts */ \"(ssr)/./node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Cell_Pie_PieChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Cell,Pie,PieChart,ResponsiveContainer!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Cell.js\");\n\n\n\n\nconst colors = [\n    \"#00C49F\",\n    \"#0088FE\",\n    \"#FFBB28\"\n];\nconst CardExpenseSummary = ()=>{\n    const { data: dashboardMetrics, isLoading } = (0,_state_api__WEBPACK_IMPORTED_MODULE_1__.useGetDashboardMetricsQuery)();\n    const expenseSummary = dashboardMetrics?.expenseSummary[0];\n    const expenseByCategorySummary = dashboardMetrics?.expenseByCategorySummary || [];\n    const expenseSums = expenseByCategorySummary.reduce((acc, item)=>{\n        const category = item.category + \" Expenses\";\n        const amount = parseInt(item.amount, 10);\n        if (!acc[category]) acc[category] = 0;\n        acc[category] += amount;\n        return acc;\n    }, {});\n    const expenseCategories = Object.entries(expenseSums).map(([name, value])=>({\n            name,\n            value\n        }));\n    const totalExpenses = expenseCategories.reduce((acc, category)=>acc + category.value, 0);\n    const formattedTotalExpenses = totalExpenses.toFixed(2);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"row-span-3 bg-white shadow-md rounded-2xl flex flex-col justify-between\",\n        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"m-5\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n            lineNumber: 49,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold mb-2 px-7 pt-5\",\n                            children: \"Expense Summary\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"xl:flex justify-between pr-7\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative basis-3/5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cell_Pie_PieChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_2__.ResponsiveContainer, {\n                                    width: \"100%\",\n                                    height: 140,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cell_Pie_PieChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_3__.PieChart, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cell_Pie_PieChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_4__.Pie, {\n                                            data: expenseCategories,\n                                            innerRadius: 50,\n                                            outerRadius: 60,\n                                            fill: \"#8884d8\",\n                                            dataKey: \"value\",\n                                            nameKey: \"name\",\n                                            cx: \"50%\",\n                                            cy: \"50%\",\n                                            children: expenseCategories.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Cell_Pie_PieChart_ResponsiveContainer_recharts__WEBPACK_IMPORTED_MODULE_5__.Cell, {\n                                                    fill: colors[index % colors.length]\n                                                }, `cell-${index}`, false, {\n                                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 23\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-center basis-2/5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-bold text-xl\",\n                                        children: [\n                                            \"$\",\n                                            formattedTotalExpenses\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"flex flex-col justify-around items-center xl:items-start py-5 gap-3\",\n                            children: expenseCategories.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"flex items-center text-xs\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2 w-3 h-3 rounded-full\",\n                                            style: {\n                                                backgroundColor: colors[index % colors.length]\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        entry.name\n                                    ]\n                                }, `legend-${index}`, true, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 13\n                        }, undefined),\n                        expenseSummary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-3 flex justify-between items-center px-7 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            \"Average:\",\n                                            \" \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-semibold\",\n                                                children: [\n                                                    \"$\",\n                                                    expenseSummary.totalExpenses.toFixed(2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"flex items-center mt-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"mr-2 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        \"30%\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 15\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardExpenseSummary.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CardExpenseSummary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/CardExpenseSummary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/CardPopularProducts.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/CardPopularProducts.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _state_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/state/api */ \"(ssr)/./src/state/api.ts\");\n/* harmony import */ var _barrel_optimize_names_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ShoppingBag!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shopping-bag.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_Rating__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../(components)/Rating */ \"(ssr)/./src/app/(components)/Rating/index.tsx\");\n\n\n\n\n\nconst CardPopularProducts = ()=>{\n    const { data: dashboardMetrics, isLoading } = (0,_state_api__WEBPACK_IMPORTED_MODULE_1__.useGetDashboardMetricsQuery)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"row-span-3 xl:row-span-6 bg-white shadow-md rounded-2xl pb-16\",\n        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"m-5\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n            lineNumber: 13,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold px-7 pt-5 pb-2\",\n                    children: \"Popular Products\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"overflow-auto h-full\",\n                    children: dashboardMetrics?.popularProducts.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between gap-3 px-5 py-7 border-b\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"img\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col justify-between gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-bold text-gray-700\",\n                                                    children: product.name\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                                    lineNumber: 29,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex text-sm items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-bold text-blue-500 text-xs\",\n                                                            children: [\n                                                                \"$\",\n                                                                product.price\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                                            lineNumber: 33,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"mx-2\",\n                                                            children: \"|\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                                            lineNumber: 36,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Rating__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                            rating: product.rating || 0\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                                            lineNumber: 37,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 17\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"p-2 rounded-full bg-blue-100 text-blue-600 mr-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ShoppingBag_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        Math.round(product.stockQuantity / 1000),\n                                        \"k Sold\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 17\n                                }, undefined)\n                            ]\n                        }, product.productId, true, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 15\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPopularProducts.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CardPopularProducts);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/CardPopularProducts.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/CardPurchaseSummary.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/CardPurchaseSummary.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _state_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/state/api */ \"(ssr)/./src/state/api.ts\");\n/* harmony import */ var _barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var numeral__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! numeral */ \"(ssr)/./node_modules/numeral/numeral.js\");\n/* harmony import */ var numeral__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(numeral__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/AreaChart.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Area,AreaChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Area.js\");\n\n\n\n\n\n\nconst CardPurchaseSummary = ()=>{\n    const { data, isLoading } = (0,_state_api__WEBPACK_IMPORTED_MODULE_1__.useGetDashboardMetricsQuery)();\n    const purchaseData = data?.purchaseSummary || [];\n    const lastDataPoint = purchaseData[purchaseData.length - 1] || null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col justify-between row-span-2 xl:row-span-3 col-span-1 md:col-span-2 xl:col-span-1 bg-white shadow-md rounded-2xl\",\n        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"m-5\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n            lineNumber: 23,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold mb-2 px-7 pt-5\",\n                            children: \"Purchase Summary\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4 mt-7 px-7\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-400\",\n                                    children: \"Purchased\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: lastDataPoint ? numeral__WEBPACK_IMPORTED_MODULE_2___default()(lastDataPoint.totalPurchased).format(\"$0.00a\") : \"0\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        lastDataPoint && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: `text-sm ${lastDataPoint.changePercentage >= 0 ? \"text-green-500\" : \"text-red-500\"} flex ml-3`,\n                                            children: [\n                                                lastDataPoint.changePercentage >= 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                                    lineNumber: 54,\n                                                    columnNumber: 23\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-5 h-5 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                                    lineNumber: 56,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                Math.abs(lastDataPoint.changePercentage),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.ResponsiveContainer, {\n                            width: \"100%\",\n                            height: 200,\n                            className: \"p-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.AreaChart, {\n                                data: purchaseData,\n                                margin: {\n                                    top: 0,\n                                    right: 0,\n                                    left: -50,\n                                    bottom: 45\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.XAxis, {\n                                        dataKey: \"date\",\n                                        tick: false,\n                                        axisLine: false\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.YAxis, {\n                                        tickLine: false,\n                                        tick: false,\n                                        axisLine: false\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                        formatter: (value)=>[\n                                                `$${value.toLocaleString(\"en\")}`\n                                            ],\n                                        labelFormatter: (label)=>{\n                                            const date = new Date(label);\n                                            return date.toLocaleDateString(\"en-US\", {\n                                                year: \"numeric\",\n                                                month: \"long\",\n                                                day: \"numeric\"\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Area_AreaChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.Area, {\n                                        type: \"linear\",\n                                        dataKey: \"totalPurchased\",\n                                        stroke: \"#8884d8\",\n                                        fill: \"#8884d8\",\n                                        dot: true\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardPurchaseSummary.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CardPurchaseSummary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/CardPurchaseSummary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/CardSalesSummary.tsx":
/*!************************************************!*\
  !*** ./src/app/dashboard/CardSalesSummary.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _state_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/state/api */ \"(ssr)/./src/state/api.ts\");\n/* harmony import */ var _barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(ssr)/./node_modules/recharts/es6/cartesian/Bar.js\");\n\n\n\n\n\nconst CardSalesSummary = ()=>{\n    const { data, isLoading, isError } = (0,_state_api__WEBPACK_IMPORTED_MODULE_1__.useGetDashboardMetricsQuery)();\n    const salesData = data?.salesSummary || [];\n    const [timeframe, setTimeframe] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"weekly\");\n    const totalValueSum = salesData.reduce((acc, curr)=>acc + curr.totalValue, 0) || 0;\n    const averageChangePercentage = salesData.reduce((acc, curr, _, array)=>{\n        return acc + curr.changePercentage / array.length;\n    }, 0) || 0;\n    const highestValueData = salesData.reduce((acc, curr)=>{\n        return acc.totalValue > curr.totalValue ? acc : curr;\n    }, salesData[0] || {});\n    const highestValueDate = highestValueData.date ? new Date(highestValueData.date).toLocaleDateString(\"en-US\", {\n        month: \"numeric\",\n        day: \"numeric\",\n        year: \"2-digit\"\n    }) : \"N/A\";\n    if (isError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"m-5\",\n            children: \"Failed to fetch data\"\n        }, void 0, false, {\n            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n            lineNumber: 41,\n            columnNumber: 12\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"row-span-3 xl:row-span-6 bg-white shadow-md rounded-2xl flex flex-col justify-between\",\n        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"m-5\",\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n            lineNumber: 47,\n            columnNumber: 9\n        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-lg font-semibold mb-2 px-7 pt-5\",\n                            children: \"Sales Summary\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mb-6 px-7 mt-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-lg font-medium\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-gray-400\",\n                                            children: \"Value\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-extrabold\",\n                                            children: [\n                                                \"$\",\n                                                (totalValueSum / 1000000).toLocaleString(\"en\", {\n                                                    maximumFractionDigits: 2\n                                                }),\n                                                \"m\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-green-500 text-sm ml-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    className: \"inline w-4 h-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                averageChangePercentage.toFixed(2),\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    className: \"shadow-sm border border-gray-300 bg-white p-2 rounded\",\n                                    value: timeframe,\n                                    onChange: (e)=>{\n                                        setTimeframe(e.target.value);\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"daily\",\n                                            children: \"Daily\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"weekly\",\n                                            children: \"Weekly\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                            lineNumber: 84,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: \"monthly\",\n                                            children: \"Monthly\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.ResponsiveContainer, {\n                            width: \"100%\",\n                            height: 350,\n                            className: \"px-7\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.BarChart, {\n                                data: salesData,\n                                margin: {\n                                    top: 0,\n                                    right: 0,\n                                    left: -25,\n                                    bottom: 0\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.CartesianGrid, {\n                                        strokeDasharray: \"\",\n                                        vertical: false\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.XAxis, {\n                                        dataKey: \"date\",\n                                        tickFormatter: (value)=>{\n                                            const date = new Date(value);\n                                            return `${date.getMonth() + 1}/${date.getDate()}`;\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.YAxis, {\n                                        tickFormatter: (value)=>{\n                                            return `$${(value / 1000000).toFixed(0)}m`;\n                                        },\n                                        tick: {\n                                            fontSize: 12,\n                                            dx: -1\n                                        },\n                                        tickLine: false,\n                                        axisLine: false\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.Tooltip, {\n                                        formatter: (value)=>[\n                                                `$${value.toLocaleString(\"en\")}`\n                                            ],\n                                        labelFormatter: (label)=>{\n                                            const date = new Date(label);\n                                            return date.toLocaleDateString(\"en-US\", {\n                                                year: \"numeric\",\n                                                month: \"long\",\n                                                day: \"numeric\"\n                                            });\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.Bar, {\n                                        dataKey: \"totalValue\",\n                                        fill: \"#3182ce\",\n                                        barSize: 10,\n                                        radius: [\n                                            10,\n                                            10,\n                                            0,\n                                            0\n                                        ]\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                        lineNumber: 123,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                            lineNumber: 89,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-between items-center mt-6 text-sm px-7 mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        salesData.length || 0,\n                                        \" days\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm\",\n                                    children: [\n                                        \"Highest Sales Date:\",\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold\",\n                                            children: highestValueDate\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\CardSalesSummary.tsx\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CardSalesSummary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/CardSalesSummary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/StatCard.tsx":
/*!****************************************!*\
  !*** ./src/app/dashboard/StatCard.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst StatCard = ({ title, primaryIcon, details, dateRange })=>{\n    const formatPercentage = (value)=>{\n        const signal = value >= 0 ? \"+\" : \"\";\n        return `${signal}${value.toFixed()}%`;\n    };\n    const getChangeColor = (value)=>value >= 0 ? \"text-green-500\" : \"text-red-500\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"md:row-span-1 xl:row-span-2 bg-white col-span-1 shadow-md rounded-2xl flex flex-col justify-between\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-2 px-5 pt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-semibold text-lg text-gray-700\",\n                                children: title\n                            }, void 0, false, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-xs text-gray-400\",\n                                children: dateRange\n                            }, void 0, false, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                        lineNumber: 40,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex mb-6 items-center justify-around gap-4 px-5\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-full p-5 bg-blue-50 border-sky-300 border-[1px]\",\n                        children: primaryIcon\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1\",\n                        children: details.map((detail, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between my-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500\",\n                                                children: detail.title\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                                                lineNumber: 52,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-bold text-gray-800\",\n                                                children: detail.amount\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(detail.IconComponent, {\n                                                        className: `w-4 h-4 mr-1 ${getChangeColor(detail.changePercentage)}`\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                                                        lineNumber: 55,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `font-medium ${getChangeColor(detail.changePercentage)}`,\n                                                        children: formatPercentage(detail.changePercentage)\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                                                        lineNumber: 61,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                                                lineNumber: 54,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    index < details.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {}, void 0, false, {\n                                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 46\n                                    }, undefined)\n                                ]\n                            }, index, true, {\n                                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\StatCard.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (StatCard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Rhc2hib2FyZC9TdGF0Q2FyZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQzBCO0FBZ0IxQixNQUFNQyxXQUFXLENBQUMsRUFDaEJDLEtBQUssRUFDTEMsV0FBVyxFQUNYQyxPQUFPLEVBQ1BDLFNBQVMsRUFDSztJQUNkLE1BQU1DLG1CQUFtQixDQUFDQztRQUN4QixNQUFNQyxTQUFTRCxTQUFTLElBQUksTUFBTTtRQUNsQyxPQUFPLENBQUMsRUFBRUMsT0FBTyxFQUFFRCxNQUFNRSxPQUFPLEdBQUcsQ0FBQyxDQUFDO0lBQ3ZDO0lBRUEsTUFBTUMsaUJBQWlCLENBQUNILFFBQ3RCQSxTQUFTLElBQUksbUJBQW1CO0lBRWxDLHFCQUNFLDhEQUFDSTtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7O2tDQUNDLDhEQUFDQTt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNDO2dDQUFHRCxXQUFVOzBDQUF1Q1Y7Ozs7OzswQ0FDckQsOERBQUNZO2dDQUFLRixXQUFVOzBDQUF5QlA7Ozs7Ozs7Ozs7OztrQ0FFM0MsOERBQUNVOzs7Ozs7Ozs7OzswQkFJSCw4REFBQ0o7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDWlQ7Ozs7OztrQ0FFSCw4REFBQ1E7d0JBQUlDLFdBQVU7a0NBQ1pSLFFBQVFZLEdBQUcsQ0FBQyxDQUFDQyxRQUFRQyxzQkFDcEIsOERBQUNsQix1REFBYzs7a0RBQ2IsOERBQUNXO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQUtGLFdBQVU7MERBQWlCSyxPQUFPZixLQUFLOzs7Ozs7MERBQzdDLDhEQUFDWTtnREFBS0YsV0FBVTswREFBMkJLLE9BQU9HLE1BQU07Ozs7OzswREFDeEQsOERBQUNUO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ0ssT0FBT0ksYUFBYTt3REFDbkJULFdBQVcsQ0FBQyxhQUFhLEVBQUVGLGVBQ3pCTyxPQUFPSyxnQkFBZ0IsRUFDdkIsQ0FBQzs7Ozs7O2tFQUdMLDhEQUFDUjt3REFDQ0YsV0FBVyxDQUFDLFlBQVksRUFBRUYsZUFDeEJPLE9BQU9LLGdCQUFnQixFQUN2QixDQUFDO2tFQUVGaEIsaUJBQWlCVyxPQUFPSyxnQkFBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7OztvQ0FJOUNKLFFBQVFkLFFBQVFtQixNQUFNLEdBQUcsbUJBQUssOERBQUNSOzs7Ozs7K0JBcEJiRzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQTJCakM7QUFFQSxpRUFBZWpCLFFBQVFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnZlbnRvcnktbWFuYWdlbWVudC8uL3NyYy9hcHAvZGFzaGJvYXJkL1N0YXRDYXJkLnRzeD81OTE3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEx1Y2lkZUljb24gfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5cbnR5cGUgU3RhdERldGFpbCA9IHtcbiAgdGl0bGU6IHN0cmluZztcbiAgYW1vdW50OiBzdHJpbmc7XG4gIGNoYW5nZVBlcmNlbnRhZ2U6IG51bWJlcjtcbiAgSWNvbkNvbXBvbmVudDogTHVjaWRlSWNvbjtcbn07XG5cbnR5cGUgU3RhdENhcmRQcm9wcyA9IHtcbiAgdGl0bGU6IHN0cmluZztcbiAgcHJpbWFyeUljb246IEpTWC5FbGVtZW50O1xuICBkZXRhaWxzOiBTdGF0RGV0YWlsW107XG4gIGRhdGVSYW5nZTogc3RyaW5nO1xufTtcblxuY29uc3QgU3RhdENhcmQgPSAoe1xuICB0aXRsZSxcbiAgcHJpbWFyeUljb24sXG4gIGRldGFpbHMsXG4gIGRhdGVSYW5nZSxcbn06IFN0YXRDYXJkUHJvcHMpID0+IHtcbiAgY29uc3QgZm9ybWF0UGVyY2VudGFnZSA9ICh2YWx1ZTogbnVtYmVyKSA9PiB7XG4gICAgY29uc3Qgc2lnbmFsID0gdmFsdWUgPj0gMCA/IFwiK1wiIDogXCJcIjtcbiAgICByZXR1cm4gYCR7c2lnbmFsfSR7dmFsdWUudG9GaXhlZCgpfSVgO1xuICB9O1xuXG4gIGNvbnN0IGdldENoYW5nZUNvbG9yID0gKHZhbHVlOiBudW1iZXIpID0+XG4gICAgdmFsdWUgPj0gMCA/IFwidGV4dC1ncmVlbi01MDBcIiA6IFwidGV4dC1yZWQtNTAwXCI7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1kOnJvdy1zcGFuLTEgeGw6cm93LXNwYW4tMiBiZy13aGl0ZSBjb2wtc3Bhbi0xIHNoYWRvdy1tZCByb3VuZGVkLTJ4bCBmbGV4IGZsZXgtY29sIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgey8qIEhFQURFUiAqL31cbiAgICAgIDxkaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW4gaXRlbXMtY2VudGVyIG1iLTIgcHgtNSBwdC00XCI+XG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1sZyB0ZXh0LWdyYXktNzAwXCI+e3RpdGxlfTwvaDI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNDAwXCI+e2RhdGVSYW5nZX08L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuICAgICAgICA8aHIgLz5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQk9EWSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBtYi02IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWFyb3VuZCBnYXAtNCBweC01XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm91bmRlZC1mdWxsIHAtNSBiZy1ibHVlLTUwIGJvcmRlci1za3ktMzAwIGJvcmRlci1bMXB4XVwiPlxuICAgICAgICAgIHtwcmltYXJ5SWNvbn1cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAge2RldGFpbHMubWFwKChkZXRhaWwsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8UmVhY3QuRnJhZ21lbnQga2V5PXtpbmRleH0+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG15LTRcIj5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwXCI+e2RldGFpbC50aXRsZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1ib2xkIHRleHQtZ3JheS04MDBcIj57ZGV0YWlsLmFtb3VudH08L3NwYW4+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRldGFpbC5JY29uQ29tcG9uZW50XG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctNCBoLTQgbXItMSAke2dldENoYW5nZUNvbG9yKFxuICAgICAgICAgICAgICAgICAgICAgIGRldGFpbC5jaGFuZ2VQZXJjZW50YWdlXG4gICAgICAgICAgICAgICAgICAgICl9YH1cbiAgICAgICAgICAgICAgICAgIC8+XG5cbiAgICAgICAgICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZvbnQtbWVkaXVtICR7Z2V0Q2hhbmdlQ29sb3IoXG4gICAgICAgICAgICAgICAgICAgICAgZGV0YWlsLmNoYW5nZVBlcmNlbnRhZ2VcbiAgICAgICAgICAgICAgICAgICAgKX1gfVxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0UGVyY2VudGFnZShkZXRhaWwuY2hhbmdlUGVyY2VudGFnZSl9XG4gICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICB7aW5kZXggPCBkZXRhaWxzLmxlbmd0aCAtIDEgJiYgPGhyIC8+fVxuICAgICAgICAgICAgPC9SZWFjdC5GcmFnbWVudD5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFN0YXRDYXJkO1xuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU3RhdENhcmQiLCJ0aXRsZSIsInByaW1hcnlJY29uIiwiZGV0YWlscyIsImRhdGVSYW5nZSIsImZvcm1hdFBlcmNlbnRhZ2UiLCJ2YWx1ZSIsInNpZ25hbCIsInRvRml4ZWQiLCJnZXRDaGFuZ2VDb2xvciIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwic3BhbiIsImhyIiwibWFwIiwiZGV0YWlsIiwiaW5kZXgiLCJGcmFnbWVudCIsImFtb3VudCIsIkljb25Db21wb25lbnQiLCJjaGFuZ2VQZXJjZW50YWdlIiwibGVuZ3RoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/StatCard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Package,Tag,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Package,Tag,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Package,Tag,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Package,Tag,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Package,Tag,TrendingDown,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/tag.js\");\n/* harmony import */ var _CardExpenseSummary__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./CardExpenseSummary */ \"(ssr)/./src/app/dashboard/CardExpenseSummary.tsx\");\n/* harmony import */ var _CardPopularProducts__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CardPopularProducts */ \"(ssr)/./src/app/dashboard/CardPopularProducts.tsx\");\n/* harmony import */ var _CardPurchaseSummary__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CardPurchaseSummary */ \"(ssr)/./src/app/dashboard/CardPurchaseSummary.tsx\");\n/* harmony import */ var _CardSalesSummary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CardSalesSummary */ \"(ssr)/./src/app/dashboard/CardSalesSummary.tsx\");\n/* harmony import */ var _StatCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StatCard */ \"(ssr)/./src/app/dashboard/StatCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst Dashboard = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 xl:overflow-auto gap-10 pb-4 custom-grid-rows\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CardPopularProducts__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CardSalesSummary__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CardPurchaseSummary__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CardExpenseSummary__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                title: \"Customer & Expenses\",\n                primaryIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"text-blue-600 w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 22\n                }, void 0),\n                dateRange: \"22 - 29 October 2023\",\n                details: [\n                    {\n                        title: \"Customer Growth\",\n                        amount: \"175.00\",\n                        changePercentage: 131,\n                        IconComponent: _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                    },\n                    {\n                        title: \"Expenses\",\n                        amount: \"10.00\",\n                        changePercentage: -56,\n                        IconComponent: _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                    }\n                ]\n            }, void 0, false, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                title: \"Dues & Pending Orders\",\n                primaryIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"text-blue-600 w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 22\n                }, void 0),\n                dateRange: \"22 - 29 October 2023\",\n                details: [\n                    {\n                        title: \"Dues\",\n                        amount: \"250.00\",\n                        changePercentage: 131,\n                        IconComponent: _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                    },\n                    {\n                        title: \"Pending Orders\",\n                        amount: \"147\",\n                        changePercentage: -56,\n                        IconComponent: _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                    }\n                ]\n            }, void 0, false, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StatCard__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                title: \"Sales & Discount\",\n                primaryIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"text-blue-600 w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 22\n                }, void 0),\n                dateRange: \"22 - 29 October 2023\",\n                details: [\n                    {\n                        title: \"Sales\",\n                        amount: \"1000.00\",\n                        changePercentage: 20,\n                        IconComponent: _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n                    },\n                    {\n                        title: \"Discount\",\n                        amount: \"200.00\",\n                        changePercentage: -10,\n                        IconComponent: _barrel_optimize_names_CheckCircle_Package_Tag_TrendingDown_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n                    }\n                ]\n            }, void 0, false, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Dashboard);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/redux.tsx":
/*!***************************!*\
  !*** ./src/app/redux.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StoreProvider),\n/* harmony export */   makeStore: () => (/* binding */ makeStore),\n/* harmony export */   useAppDispatch: () => (/* binding */ useAppDispatch),\n/* harmony export */   useAppSelector: () => (/* binding */ useAppSelector)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/redux/dist/redux.mjs\");\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react-redux */ \"(ssr)/./node_modules/react-redux/dist/react-redux.mjs\");\n/* harmony import */ var _state__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/state */ \"(ssr)/./src/state/index.ts\");\n/* harmony import */ var _state_api__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/state/api */ \"(ssr)/./src/state/api.ts\");\n/* harmony import */ var _reduxjs_toolkit_query__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @reduxjs/toolkit/query */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n/* harmony import */ var redux_persist__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! redux-persist */ \"(ssr)/./node_modules/redux-persist/es/index.js\");\n/* harmony import */ var redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! redux-persist/integration/react */ \"(ssr)/./node_modules/redux-persist/es/integration/react.js\");\n/* harmony import */ var redux_persist_lib_storage_createWebStorage__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! redux-persist/lib/storage/createWebStorage */ \"(ssr)/./node_modules/redux-persist/lib/storage/createWebStorage.js\");\n\n\n\n\n\n\n\n\n\n\n/* REDUX PERSISTENCE */ const createNoopStorage = ()=>{\n    return {\n        getItem (_key) {\n            return Promise.resolve(null);\n        },\n        setItem (_key, value) {\n            return Promise.resolve(value);\n        },\n        removeItem (_key) {\n            return Promise.resolve();\n        }\n    };\n};\nconst storage =  true ? createNoopStorage() : 0;\nconst persistConfig = {\n    key: \"root\",\n    storage,\n    whitelist: [\n        \"global\"\n    ]\n};\nconst rootReducer = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_7__.combineReducers)({\n    global: _state__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    [_state_api__WEBPACK_IMPORTED_MODULE_3__.api.reducerPath]: _state_api__WEBPACK_IMPORTED_MODULE_3__.api.reducer\n});\nconst persistedReducer = (0,redux_persist__WEBPACK_IMPORTED_MODULE_4__.persistReducer)(persistConfig, rootReducer);\n/* REDUX STORE */ const makeStore = ()=>{\n    return (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_8__.configureStore)({\n        reducer: persistedReducer,\n        middleware: (getDefaultMiddleware)=>getDefaultMiddleware({\n                serializableCheck: {\n                    ignoredActions: [\n                        redux_persist__WEBPACK_IMPORTED_MODULE_4__.FLUSH,\n                        redux_persist__WEBPACK_IMPORTED_MODULE_4__.REHYDRATE,\n                        redux_persist__WEBPACK_IMPORTED_MODULE_4__.PAUSE,\n                        redux_persist__WEBPACK_IMPORTED_MODULE_4__.PERSIST,\n                        redux_persist__WEBPACK_IMPORTED_MODULE_4__.PURGE,\n                        redux_persist__WEBPACK_IMPORTED_MODULE_4__.REGISTER\n                    ]\n                }\n            }).concat(_state_api__WEBPACK_IMPORTED_MODULE_3__.api.middleware)\n    });\n};\nconst useAppDispatch = ()=>(0,react_redux__WEBPACK_IMPORTED_MODULE_9__.useDispatch)();\nconst useAppSelector = react_redux__WEBPACK_IMPORTED_MODULE_9__.useSelector;\n/* PROVIDER */ function StoreProvider({ children }) {\n    const storeRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    if (!storeRef.current) {\n        storeRef.current = makeStore();\n        (0,_reduxjs_toolkit_query__WEBPACK_IMPORTED_MODULE_10__.setupListeners)(storeRef.current.dispatch);\n    }\n    const persistor = (0,redux_persist__WEBPACK_IMPORTED_MODULE_4__.persistStore)(storeRef.current);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_redux__WEBPACK_IMPORTED_MODULE_9__.Provider, {\n        store: storeRef.current,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(redux_persist_integration_react__WEBPACK_IMPORTED_MODULE_5__.PersistGate, {\n            loading: null,\n            persistor: persistor,\n            children: children\n        }, void 0, false, {\n            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\redux.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\redux.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/redux.tsx\n");

/***/ }),

/***/ "(ssr)/./src/state/api.ts":
/*!**************************!*\
  !*** ./src/state/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   api: () => (/* binding */ api),\n/* harmony export */   useCreateProductMutation: () => (/* binding */ useCreateProductMutation),\n/* harmony export */   useGetDashboardMetricsQuery: () => (/* binding */ useGetDashboardMetricsQuery),\n/* harmony export */   useGetExpensesByCategoryQuery: () => (/* binding */ useGetExpensesByCategoryQuery),\n/* harmony export */   useGetProductsQuery: () => (/* binding */ useGetProductsQuery),\n/* harmony export */   useGetUsersQuery: () => (/* binding */ useGetUsersQuery)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n\nconst api = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__.createApi)({\n    baseQuery: (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.fetchBaseQuery)({\n        baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL\n    }),\n    reducerPath: \"api\",\n    tagTypes: [\n        \"DashboardMetrics\",\n        \"Products\",\n        \"Users\",\n        \"Expenses\"\n    ],\n    endpoints: (build)=>({\n            getDashboardMetrics: build.query({\n                query: ()=>\"/dashboard\",\n                providesTags: [\n                    \"DashboardMetrics\"\n                ]\n            }),\n            getProducts: build.query({\n                query: (search)=>({\n                        url: \"/products\",\n                        params: search ? {\n                            search\n                        } : {}\n                    }),\n                providesTags: [\n                    \"Products\"\n                ]\n            }),\n            createProduct: build.mutation({\n                query: (newProduct)=>({\n                        url: \"/products\",\n                        method: \"POST\",\n                        body: newProduct\n                    }),\n                invalidatesTags: [\n                    \"Products\"\n                ]\n            }),\n            getUsers: build.query({\n                query: ()=>\"/users\",\n                providesTags: [\n                    \"Users\"\n                ]\n            }),\n            getExpensesByCategory: build.query({\n                query: ()=>\"/expenses\",\n                providesTags: [\n                    \"Expenses\"\n                ]\n            })\n        })\n});\nconst { useGetDashboardMetricsQuery, useGetProductsQuery, useCreateProductMutation, useGetUsersQuery, useGetExpensesByCategoryQuery } = api;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/state/api.ts\n");

/***/ }),

/***/ "(ssr)/./src/state/index.ts":
/*!****************************!*\
  !*** ./src/state/index.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   globalSlice: () => (/* binding */ globalSlice),\n/* harmony export */   setIsDarkMode: () => (/* binding */ setIsDarkMode),\n/* harmony export */   setIsSidebarCollapsed: () => (/* binding */ setIsSidebarCollapsed)\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(ssr)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n\nconst initialState = {\n    isSidebarCollapsed: false,\n    isDarkMode: false\n};\nconst globalSlice = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_0__.createSlice)({\n    name: \"global\",\n    initialState,\n    reducers: {\n        setIsSidebarCollapsed: (state, action)=>{\n            state.isSidebarCollapsed = action.payload;\n        },\n        setIsDarkMode: (state, action)=>{\n            state.isDarkMode = action.payload;\n        }\n    }\n});\nconst { setIsSidebarCollapsed, setIsDarkMode } = globalSlice.actions;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (globalSlice.reducer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc3RhdGUvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBOEQ7QUFPOUQsTUFBTUMsZUFBa0M7SUFDdENDLG9CQUFvQjtJQUNwQkMsWUFBWTtBQUNkO0FBRU8sTUFBTUMsY0FBY0osNkRBQVdBLENBQUM7SUFDckNLLE1BQU07SUFDTko7SUFDQUssVUFBVTtRQUNSQyx1QkFBdUIsQ0FBQ0MsT0FBT0M7WUFDN0JELE1BQU1OLGtCQUFrQixHQUFHTyxPQUFPQyxPQUFPO1FBQzNDO1FBQ0FDLGVBQWUsQ0FBQ0gsT0FBT0M7WUFDckJELE1BQU1MLFVBQVUsR0FBR00sT0FBT0MsT0FBTztRQUNuQztJQUNGO0FBQ0YsR0FBRztBQUVJLE1BQU0sRUFBRUgscUJBQXFCLEVBQUVJLGFBQWEsRUFBRSxHQUFHUCxZQUFZUSxPQUFPLENBQUM7QUFFNUUsaUVBQWVSLFlBQVlTLE9BQU8sRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ludmVudG9yeS1tYW5hZ2VtZW50Ly4vc3JjL3N0YXRlL2luZGV4LnRzPzMzZDciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY3JlYXRlU2xpY2UsIFBheWxvYWRBY3Rpb24gfSBmcm9tIFwiQHJlZHV4anMvdG9vbGtpdFwiO1xuXG5leHBvcnQgaW50ZXJmYWNlIEluaXRpYWxTdGF0ZVR5cGVzIHtcbiAgaXNTaWRlYmFyQ29sbGFwc2VkOiBib29sZWFuO1xuICBpc0RhcmtNb2RlOiBib29sZWFuO1xufVxuXG5jb25zdCBpbml0aWFsU3RhdGU6IEluaXRpYWxTdGF0ZVR5cGVzID0ge1xuICBpc1NpZGViYXJDb2xsYXBzZWQ6IGZhbHNlLFxuICBpc0RhcmtNb2RlOiBmYWxzZSxcbn07XG5cbmV4cG9ydCBjb25zdCBnbG9iYWxTbGljZSA9IGNyZWF0ZVNsaWNlKHtcbiAgbmFtZTogXCJnbG9iYWxcIixcbiAgaW5pdGlhbFN0YXRlLFxuICByZWR1Y2Vyczoge1xuICAgIHNldElzU2lkZWJhckNvbGxhcHNlZDogKHN0YXRlLCBhY3Rpb246IFBheWxvYWRBY3Rpb248Ym9vbGVhbj4pID0+IHtcbiAgICAgIHN0YXRlLmlzU2lkZWJhckNvbGxhcHNlZCA9IGFjdGlvbi5wYXlsb2FkO1xuICAgIH0sXG4gICAgc2V0SXNEYXJrTW9kZTogKHN0YXRlLCBhY3Rpb246IFBheWxvYWRBY3Rpb248Ym9vbGVhbj4pID0+IHtcbiAgICAgIHN0YXRlLmlzRGFya01vZGUgPSBhY3Rpb24ucGF5bG9hZDtcbiAgICB9LFxuICB9LFxufSk7XG5cbmV4cG9ydCBjb25zdCB7IHNldElzU2lkZWJhckNvbGxhcHNlZCwgc2V0SXNEYXJrTW9kZSB9ID0gZ2xvYmFsU2xpY2UuYWN0aW9ucztcblxuZXhwb3J0IGRlZmF1bHQgZ2xvYmFsU2xpY2UucmVkdWNlcjtcbiJdLCJuYW1lcyI6WyJjcmVhdGVTbGljZSIsImluaXRpYWxTdGF0ZSIsImlzU2lkZWJhckNvbGxhcHNlZCIsImlzRGFya01vZGUiLCJnbG9iYWxTbGljZSIsIm5hbWUiLCJyZWR1Y2VycyIsInNldElzU2lkZWJhckNvbGxhcHNlZCIsInN0YXRlIiwiYWN0aW9uIiwicGF5bG9hZCIsInNldElzRGFya01vZGUiLCJhY3Rpb25zIiwicmVkdWNlciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/state/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"dc8c2ed47388\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vaW52ZW50b3J5LW1hbmFnZW1lbnQvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzIzOTQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJkYzhjMmVkNDczODhcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/dashboardWrapper.tsx":
/*!**************************************!*\
  !*** ./src/app/dashboardWrapper.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\StockPilot\client\src\app\dashboardWrapper.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\StockPilot\client\src\app\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _dashboardWrapper__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./dashboardWrapper */ \"(rsc)/./src/app/dashboardWrapper.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_dashboardWrapper__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 21,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\StockPilot\\\\client\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBS01BO0FBSGlCO0FBQzJCO0FBSTNDLE1BQU1FLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdWLCtKQUFlO3NCQUM5Qiw0RUFBQ0MseURBQWdCQTswQkFBRUs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnZlbnRvcnktbWFuYWdlbWVudC8uL3NyYy9hcHAvbGF5b3V0LnRzeD81N2E5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xuaW1wb3J0IHsgSW50ZXIgfSBmcm9tIFwibmV4dC9mb250L2dvb2dsZVwiO1xuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiO1xuaW1wb3J0IERhc2hib2FyZFdyYXBwZXIgZnJvbSBcIi4vZGFzaGJvYXJkV3JhcHBlclwiO1xuXG5jb25zdCBpbnRlciA9IEludGVyKHsgc3Vic2V0czogW1wibGF0aW5cIl0gfSk7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIkNyZWF0ZSBOZXh0IEFwcFwiLFxuICBkZXNjcmlwdGlvbjogXCJHZW5lcmF0ZWQgYnkgY3JlYXRlIG5leHQgYXBwXCIsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiBSZWFkb25seTx7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtpbnRlci5jbGFzc05hbWV9PlxuICAgICAgICA8RGFzaGJvYXJkV3JhcHBlcj57Y2hpbGRyZW59PC9EYXNoYm9hcmRXcmFwcGVyPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJpbnRlciIsIkRhc2hib2FyZFdyYXBwZXIiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9pbnZlbnRvcnktbWFuYWdlbWVudC8uL3NyYy9hcHAvZmF2aWNvbi5pY28/YThkYyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/recharts","vendor-chunks/@reduxjs","vendor-chunks/lodash","vendor-chunks/d3-shape","vendor-chunks/react-smooth","vendor-chunks/decimal.js-light","vendor-chunks/react-redux","vendor-chunks/immer","vendor-chunks/redux-persist","vendor-chunks/numeral","vendor-chunks/d3-scale","vendor-chunks/react-transition-group","vendor-chunks/prop-types","vendor-chunks/fast-equals","vendor-chunks/d3-time-format","vendor-chunks/recharts-scale","vendor-chunks/lucide-react","vendor-chunks/redux","vendor-chunks/d3-time","vendor-chunks/d3-format","vendor-chunks/d3-array","vendor-chunks/d3-color","vendor-chunks/eventemitter3","vendor-chunks/d3-interpolate","vendor-chunks/react-is","vendor-chunks/use-sync-external-store","vendor-chunks/d3-path","vendor-chunks/@swc","vendor-chunks/object-assign","vendor-chunks/internmap","vendor-chunks/@babel","vendor-chunks/victory-vendor","vendor-chunks/tiny-invariant","vendor-chunks/redux-thunk","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=F%3A%5CStockPilot%5Cclient%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CStockPilot%5Cclient&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();