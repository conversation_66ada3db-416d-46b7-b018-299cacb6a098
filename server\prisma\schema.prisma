// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Users {
  userId String @id
  name   String
  email  String
}

model Products {
  productId     String      @id
  name          String
  price         Float
  rating        Float?
  stockQuantity Int
  Sales         Sales[]
  Purchases     Purchases[]
}

model Sales {
  saleId      String   @id
  productId   String
  timestamp   DateTime
  quantity    Int
  unitPrice   Float
  totalAmount Float
  product     Products @relation(fields: [productId], references: [productId])
}

model Purchases {
  purchaseId String   @id
  productId  String
  timestamp  DateTime
  quantity   Int
  unitCost   Float
  totalCost  Float
  product    Products @relation(fields: [productId], references: [productId])
}

model Expenses {
  expenseId String   @id
  category  String
  amount    Float
  timestamp DateTime
}

model SalesSummary {
  salesSummaryId   String   @id
  totalValue       Float
  changePercentage Float?
  date             DateTime
}

model PurchaseSummary {
  purchaseSummaryId String   @id
  totalPurchased    Float
  changePercentage  Float?
  date              DateTime
}

model ExpenseSummary {
  expenseSummaryId  String              @id
  totalExpenses     Float
  date              DateTime
  ExpenseByCategory ExpenseByCategory[]
}

model ExpenseByCategory {
  expenseByCategoryId String         @id
  expenseSummaryId    String
  category            String
  amount              BigInt
  date                DateTime
  expenseSummary      ExpenseSummary @relation(fields: [expenseSummaryId], references: [expenseSummaryId])
}
