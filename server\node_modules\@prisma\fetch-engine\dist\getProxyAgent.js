"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var getProxyAgent_exports = {};
__export(getProxyAgent_exports, {
  getProxyAgent: () => import_chunk_LOOX3H4O.getProxyAgent
});
module.exports = __toCommonJS(getProxyAgent_exports);
var import_chunk_LOOX3H4O = require("./chunk-LOOX3H4O.js");
var import_chunk_QGM4M3NI = require("./chunk-QGM4M3NI.js");
