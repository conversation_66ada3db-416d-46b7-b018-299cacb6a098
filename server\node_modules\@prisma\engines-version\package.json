{"name": "@prisma/engines-version", "version": "6.8.0-43.2060c79ba17c6bb9f5823312b6f6b7f4a845738e", "main": "index.js", "types": "index.d.ts", "license": "Apache-2.0", "author": "<PERSON> <suchane<PERSON>@prisma.io>", "prisma": {"enginesVersion": "2060c79ba17c6bb9f5823312b6f6b7f4a845738e"}, "repository": {"type": "git", "url": "https://github.com/prisma/engines-wrapper.git", "directory": "packages/engines-version"}, "devDependencies": {"@types/node": "18.19.76", "typescript": "4.9.5"}, "files": ["index.js", "index.d.ts"], "scripts": {"build": "tsc -d"}}