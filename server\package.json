{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "seed": "ts-node prisma/seed.ts", "build": "rimraf dist && npx tsc", "start": "npm run build && node dist/index.js", "dev": "npm run build && concurrently \"npx tsc -w\" \"nodemon --exec ts-node src/index.ts\""}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.8.2", "body-parser": "^1.20.2", "concurrently": "^8.2.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "helmet": "^7.1.0", "morgan": "^1.10.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/morgan": "^1.9.9", "@types/node": "^20.14.10", "nodemon": "^3.1.4", "prisma": "^6.8.2", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.5.3"}}